# AI Resume Builder

This is a Next.js application that functions as an AI-powered resume builder. Users can interact with an AI assistant to generate and edit their resume, which is displayed in real-time. The application supports both English and Chinese languages.

## Features

- **AI-powered resume generation and editing:** Interact with an AI assistant to create and modify your resume.
- **Real-time preview:** See your resume updates as you make them.
- **In-place editing:** Click on any field in the resume preview to edit it directly.
- **Internationalization:** Supports both English and Chinese.
- **PDF export:** Export your resume as a PDF file.

## Tech Stack

- **Framework:** [Next.js](https://nextjs.org/) (App Router)
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/) with [shadcn/ui](https://ui.shadcn.com/)
- **State Management:** [Zustand](https://zustand-demo.pmnd.rs/)
- **Internationalization (i18n):** [`next-intl`](https://next-intl-docs.vercel.app/)

## Getting Started

First, install the dependencies:

```bash
yarn install
```

Then, run the development server:

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

- `app/[locale]/page.tsx`: The main page component, which lays out the chat and resume preview areas.
- `app/components/chat-area.tsx`: The component for the AI chat interface.
- `app/components/resume-preview.tsx`: The component that displays the resume.
- `app/api/chat/route.ts`: The API route that handles chat messages and resume data updates.
- `app/store/chat-store.ts`: The Zustand store for managing application state.
- `app/lib/types.ts`: TypeScript type definitions for the resume data structure.
- `messages/{en,zh}.json`: Language files for `next-intl`.
- `i18n/`: Configuration for `next-intl`.

## Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
