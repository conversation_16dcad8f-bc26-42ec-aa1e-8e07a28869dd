# 会员功能部署指南

## 概述

本指南将帮助您将带有完整会员功能的简历生成器部署到生产环境。

## 部署前检查清单

### 1. 代码准备
- [x] 所有会员功能代码已完成
- [x] 数据库模型已更新
- [x] API接口已实现
- [x] 前端界面已集成
- [x] 错误处理已完善
- [x] 国际化支持已添加

### 2. 环境配置
- [ ] 生产环境变量已配置
- [ ] 数据库连接已设置
- [ ] 支付宝正式应用已申请
- [ ] 域名和SSL证书已准备

### 3. 测试验证
- [ ] 本地功能测试通过
- [ ] 支付流程测试完成
- [ ] 错误处理验证通过
- [ ] 性能测试满足要求

## 环境变量配置

### 生产环境变量 (.env.production)

```bash
# 应用配置
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_super_secure_secret_here

# 数据库配置
DATABASE_URL="mysql://username:password@host:port/database_name"

# AI服务配置
OPENAI_API_KEY=your_volcano_engine_api_key
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
OPENAI_MODEL=deepseek-v3-250324

# 支付宝生产环境配置
ALIPAY_APP_ID=your_production_app_id
ALIPAY_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
your_production_private_key
-----END RSA PRIVATE KEY-----"
ALIPAY_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
alipay_production_public_key
-----END PUBLIC KEY-----"
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do

# Google OAuth (可选)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# 定时任务密钥
CRON_SECRET=your_cron_secret_for_membership_updates

# 短信服务配置 (如果使用)
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
```

## 数据库部署

### 1. 生产数据库设置

```sql
-- 创建数据库
CREATE DATABASE resume_next_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'resume_app'@'%' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON resume_next_prod.* TO 'resume_app'@'%';
FLUSH PRIVILEGES;
```

### 2. 数据库迁移

```bash
# 生成Prisma客户端
npx prisma generate

# 推送数据库结构
npx prisma db push

# 或者使用迁移（推荐）
npx prisma migrate deploy
```

### 3. 数据库索引优化

```sql
-- 为常用查询添加索引
CREATE INDEX idx_user_phone ON User(phone);
CREATE INDEX idx_user_member_status ON User(isMember, memberExpiry);
CREATE INDEX idx_order_status ON Order(status, createdAt);
CREATE INDEX idx_order_user_id ON Order(userId, createdAt);
CREATE INDEX idx_payment_order_id ON Payment(orderId);
```

## 支付宝生产环境配置

### 1. 申请正式应用
1. 登录支付宝开放平台
2. 创建网页&移动应用
3. 填写应用信息并提交审核
4. 等待审核通过

### 2. 签约产品
1. 在应用管理中添加"电脑网站支付"产品
2. 签约并等待审核通过
3. 获取正式的APPID

### 3. 配置回调地址
- 应用网关：`https://your-domain.com/api/payment/notify`
- 授权回调地址：`https://your-domain.com/payment/success`

## 部署平台选择

### 1. Vercel 部署（推荐）

```bash
# 安装Vercel CLI
npm i -g vercel

# 登录Vercel
vercel login

# 部署
vercel --prod
```

**Vercel配置文件 (vercel.json):**
```json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "DATABASE_URL": "@database-url",
    "NEXTAUTH_SECRET": "@nextauth-secret",
    "ALIPAY_APP_ID": "@alipay-app-id"
  }
}
```

### 2. Docker 部署

**Dockerfile:**
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

**docker-compose.yml:**
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - ALIPAY_APP_ID=${ALIPAY_APP_ID}
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: resume_next_prod
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"

volumes:
  mysql_data:
```

## 定时任务设置

### 1. 使用Vercel Cron Jobs

**vercel.json 添加:**
```json
{
  "crons": [
    {
      "path": "/api/cron/update-memberships",
      "schedule": "0 * * * *"
    }
  ]
}
```

### 2. 使用外部Cron服务

```bash
# 每小时执行一次
0 * * * * curl -H "Authorization: Bearer your_cron_secret" https://your-domain.com/api/cron/update-memberships
```

## 监控和日志

### 1. 错误监控
推荐使用 Sentry 进行错误监控：

```bash
npm install @sentry/nextjs
```

**sentry.client.config.js:**
```javascript
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
});
```

### 2. 性能监控
- 使用 Vercel Analytics
- 配置 Google Analytics
- 设置 Uptime 监控

### 3. 支付监控
- 监控支付成功率
- 设置支付失败告警
- 记录异常订单

## 安全配置

### 1. HTTPS 配置
- 确保所有生产环境使用HTTPS
- 配置SSL证书自动续期
- 设置HSTS头部

### 2. 环境变量安全
- 使用平台提供的环境变量管理
- 定期轮换敏感密钥
- 不在代码中硬编码密钥

### 3. 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据

## 性能优化

### 1. 数据库优化
- 配置连接池
- 添加必要索引
- 定期清理过期数据

### 2. 缓存策略
- 使用Redis缓存会员状态
- 配置CDN加速静态资源
- 启用Next.js缓存

### 3. 代码优化
- 启用代码分割
- 优化图片资源
- 压缩JavaScript和CSS

## 备份策略

### 1. 数据库备份
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -h host -u user -p database > backup_$DATE.sql
```

### 2. 文件备份
- 定期备份上传的文件
- 备份配置文件
- 版本控制代码

## 部署后验证

### 1. 功能验证
- [ ] 用户注册登录正常
- [ ] 聊天功能正常
- [ ] 会员购买流程正常
- [ ] 支付回调处理正常
- [ ] 定时任务执行正常

### 2. 性能验证
- [ ] 页面加载速度 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 数据库查询优化
- [ ] 并发处理能力

### 3. 安全验证
- [ ] HTTPS正常工作
- [ ] 支付签名验证正常
- [ ] 用户权限控制正常
- [ ] 敏感信息保护

## 故障排查

### 1. 常见问题
- 数据库连接失败
- 支付回调失败
- 会员状态不更新
- 定时任务不执行

### 2. 日志查看
```bash
# Vercel日志
vercel logs

# Docker日志
docker logs container_name

# 数据库日志
tail -f /var/log/mysql/error.log
```

### 3. 健康检查
创建健康检查端点：

```typescript
// app/api/health/route.ts
export async function GET() {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected'
    });
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      error: error.message
    }, { status: 500 });
  }
}
```

## 维护计划

### 1. 定期维护
- 每周检查系统状态
- 每月更新依赖包
- 每季度性能优化

### 2. 数据清理
- 清理过期的验证码
- 归档历史订单数据
- 清理无效的会话数据

### 3. 监控告警
- 设置错误率告警
- 配置性能下降告警
- 监控支付异常
