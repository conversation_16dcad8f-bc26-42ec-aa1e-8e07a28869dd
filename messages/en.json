{"AppHeader": {"title": "Firsume", "logout": "Logout", "profile": "Profile", "settings": "Settings"}, "ChatArea": {"title": "AI Assistant", "placeholder": "Type your instructions...", "enterToSend": "Enter to send, Shift+Enter for a new line", "initialMessage": "Hello! I'm your AI resume assistant. Sign up to get 5 free chats.\n\nYou'll see a default resume template on the right. Feel free to edit it directly, or ask me to help - just tell me what changes you'd like.\n\nTip: Use @ to reference content from other chats.", "generating": "Generating...", "processing": "Processing...", "errorMessage": "Sorry, something went wrong while processing your request. Please try again later."}, "ChatHeader": {"premium": "Premium", "upgrade": "Upgrade", "limitWarning": "Your free chat quota is running out. Upgrade to premium for unlimited access.", "upgradeNow": "Upgrade Now"}, "ArrayItemWrapper": {"deleteTitle": "Delete Item", "deleteDescription": "Are you sure you want to delete this section? This action cannot be undone."}, "TagItemWrapper": {"deleteTitle": "Delete Item", "deleteDescription": "Are you sure you want to delete this item? This action cannot be undone."}, "ConfirmDialog": {"cancel": "Cancel"}, "ResumePreview": {"title": "Preview & Edit", "exportPdf": "Export PDF", "saveResume": "Save Resume", "saveAsResume": "Save As", "placeholderTitle": "Your resume will be previewed here in real-time", "placeholderSubtitle": "Start by chatting with the AI on the left to generate your resume.", "summary": "Summary", "workExperience": "Work Experience", "education": "Education", "skills": "Skills", "projects": "Projects", "noSkills": "No skills information"}, "EditableField": {"clickToAdd": "Click to add"}, "Auth": {"loginTitle": "<PERSON><PERSON>", "loginDescription": "Log in to continue with the AI resume builder.", "phoneLogin": "Phone Login", "googleLogin": "Google Login", "phonePlaceholder": "Enter your phone number", "codePlaceholder": "Enter verification code", "sendCode": "Send Code", "sendingCode": "Sending...", "resendCode": "Resend Code", "login": "<PERSON><PERSON>", "loggingIn": "Logging in...", "continueWithGoogle": "Continue with Google", "sendCodeError": "Failed to send verification code", "loginError": "<PERSON><PERSON> failed, please try again"}, "DeleteConfirm": {"title": "Confirm Delete", "description": "This action cannot be undone. Are you sure you want to delete this?", "confirm": "Delete", "cancel": "Cancel"}, "LogoutConfirm": {"title": "Confirm <PERSON>ut", "description": "Are you sure you want to logout?", "confirm": "Logout", "cancel": "Cancel"}, "ErrorAlert": {"title": "Error", "confirm": "OK"}, "Membership": {"title": "Upgrade to Premium", "description": "Unlock all features for unlimited AI-powered resume services.", "unlimitedChats": "Unlimited Chats", "unlimitedChatsDesc": "Enjoy unlimited AI resume generation and optimization services", "priorityResponse": "Priority Response", "priorityResponseDesc": "Get faster AI response speed and priority processing", "advancedFeatures": "Advanced Features", "advancedFeaturesDesc": "Unlock more professional resume templates and customization options", "exclusiveSupport": "Exclusive Support", "exclusiveSupportDesc": "Enjoy dedicated customer support and usage guidance", "selectPackage": "Select Plan", "averagePerDay": "Average", "perDay": "/day", "payNow": "Pay Now", "pleaseSelectPackage": "Please select a package"}, "PaymentMethod": {"selectPaymentMethod": "Select Payment Method", "noPaymentMethods": "No payment methods available, please contact customer service", "recommended": "Recommended", "alipayDesc": "Supports various payment methods, including <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "wechatDesc": "Complete payment using WeChat QR code", "stripeDesc": "Supports international credit card payments"}, "ResumeNameDialog": {"title": "Name Your Resume", "placeholder": "Enter resume name", "cancel": "Cancel", "confirm": "Confirm", "saving": "Saving...", "required": "Resume name is required"}, "ResumeSwitcher": {"currentResume": "Current Resume", "switchResume": "Switch Resume", "newResume": "New Resume", "delete": "Delete", "loading": "Loading...", "noResumes": "No resumes"}, "ResumeDeleteDialog": {"title": "Confirm Delete", "description": "Are you sure you want to delete this resume? This action cannot be undone.", "resumeName": "Resume Name", "cancel": "Cancel", "confirm": "Confirm Delete", "deleting": "Deleting..."}}