# AI 聊天功能设置指南

## 概述

本项目现已集成 Vercel AI SDK，支持智能简历生成和编辑功能。系统使用火山引擎的 OpenAI 兼容接口提供真实的 AI 服务。

## 功能特性

### ✅ 已实现功能

- **流式响应**：实时显示 AI 生成内容
- **智能简历生成**：根据用户输入生成完整简历
- **简历优化**：基于现有简历进行智能优化
- **多语言支持**：支持中英文
- **错误处理**：完善的错误处理和回退机制

### 🔧 技术实现

- **Vercel AI SDK**：用于 AI 集成和流式响应
- **火山引擎 DeepSeek-V3**：通过 OpenAI 兼容接口调用的高性能模型
- **专业提示词**：针对简历生成优化的提示词模板
- **流式 UI**：支持实时显示生成内容的用户界面

## 配置步骤

1. **获取火山引擎 API 密钥**

   - 访问 [火山引擎控制台](https://console.volcengine.com/)
   - 创建账户并获取 API 密钥

2. **配置环境变量**

   ```bash
   # 编辑 .env.local 文件
   OPENAI_API_KEY=your_volcano_engine_api_key_here
   OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
   OPENAI_MODEL=deepseek-v3-250324
   ```

3. **重启开发服务器**
   ```bash
   npm run dev
   ```

## API 端点

### 1. 普通聊天 API

- **端点**：`POST /api/chat`
- **功能**：一次性返回完整响应
- **适用场景**：简单的请求-响应交互

### 2. 流式聊天 API

- **端点**：`POST /api/chat/stream`
- **功能**：实时流式返回响应
- **适用场景**：需要实时显示生成过程

### 请求格式

```json
{
  "userInput": "用户输入内容",
  "resumeData": null, // 或现有简历数据
  "locale": "zh" // 或 "en"
}
```

### 响应格式

```json
{
  "resumeData": {
    "personalInfo": {...},
    "summary": "...",
    "experience": [...],
    "education": [...],
    "skills": {...},
    "projects": [...]
  },
  "message": "AI 回复消息"
}
```

## 使用方法

### 在前端组件中使用

1. **发送消息**：输入简历相关需求
2. **查看结果**：右侧预览区域会实时更新简历内容
3. **流式响应**：可以在聊天界面勾选"流式响应"复选框来启用实时响应

### 测试功能

运行测试脚本验证功能：

```bash
node test-ai-chat.js
```

## 提示词优化

系统包含专业的简历生成提示词，支持：

- **首次生成**：根据用户基本信息生成完整简历
- **内容优化**：改进现有简历的表达和结构
- **针对性调整**：根据特定要求修改简历内容

## 成本控制

- 使用火山引擎 DeepSeek-V3 模型，成本效益优秀
- 设置合理的 token 限制（2000 tokens）
- 智能提示词设计，减少不必要的 API 调用

## 故障排除

### 常见问题

1. **API 密钥错误**

   - 检查 `.env.local` 文件中的火山引擎 API 密钥是否正确
   - 确保密钥有足够的使用额度
   - 验证 BASE_URL 和 MODEL 配置是否正确

2. **流式响应不工作**

   - 检查网络连接
   - 确认浏览器支持流式响应

3. **生成内容质量不佳**
   - 提供更详细的用户输入
   - 考虑调整提示词模板

### 调试模式

开发环境下，所有 API 调用都会在控制台输出详细日志，便于调试。

## 下一步优化建议

1. **添加更多 AI 模型支持**（Claude、Gemini 等）
2. **实现简历模板选择**
3. **添加行业特定的优化建议**
4. **支持批量简历生成**
5. **集成简历评分功能**

---

**注意**：请妥善保管您的 API 密钥，不要将其提交到版本控制系统中。
