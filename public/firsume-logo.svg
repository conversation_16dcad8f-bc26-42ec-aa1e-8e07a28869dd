<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <title>first resume by AI</title>
  <defs>
    <!-- AI 发光渐变 -->
    <linearGradient id="aiGlow" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#00F5FF"/>
      <stop offset="100%" stop-color="#7B2BFF"/>
    </linearGradient>
    <!-- 外发光滤镜 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.2" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 1 / first -->
  <line x1="4" y1="3" x2="4" y2="21" stroke="#0A0A0A" stroke-width="2"/>

  <!-- resume / 文件页 -->
  <path d="M7 4h11v16H7z" fill="none" stroke="#0A0A0A" stroke-width="1.5"/>
  <path d="M7 8h11M7 12h11M7 16h7" stroke="#0A0A0A" stroke-width="1"/>

  <!-- AI 核心 -->
  <g filter="url(#glow)">
    <!-- A -->
    <path d="M19 7l-2.5 7h1.2l.6-2h2.5l.6 2h1.2L20 7zm.1 3.5l.9-3 .9 3z" fill="url(#aiGlow)"/>
    <!-- I -->
    <rect x="20.5" y="15" width="1.5" height="5" rx=".75" fill="url(#aiGlow)"/>
  </g>

  <!-- 神经网络连线 -->
  <circle cx="20" cy="12" r=".75" fill="#00F5FF"/>
  <line x1="20" y1="12" x2="19" y2="7" stroke="#00F5FF" stroke-width=".5"/>
  <line x1="20" y1="12" x2="21" y2="15" stroke="#7B2BFF" stroke-width=".5"/>
</svg>