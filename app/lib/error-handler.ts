// 错误类型定义
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  MEMBERSHIP_ERROR = 'MEMBERSHIP_ERROR',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误信息接口
export interface AppError {
  type: ErrorType;
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// 错误消息映射
const ERROR_MESSAGES: Record<string, string> = {
  // 网络错误
  'NETWORK_ERROR': '网络连接失败，请检查网络后重试',
  'FETCH_FAILED': '请求失败，请稍后重试',
  'TIMEOUT': '请求超时，请重试',
  
  // 认证错误
  'UNAUTHORIZED': '请先登录后再使用此功能',
  'TOKEN_EXPIRED': '登录已过期，请重新登录',
  'INVALID_CREDENTIALS': '登录信息无效',
  
  // 会员相关错误
  'CHAT_LIMIT_EXCEEDED': '免费聊天次数已用完，请升级会员继续使用',
  'MEMBERSHIP_REQUIRED': '此功能需要会员权限，请先购买会员',
  'MEMBERSHIP_EXPIRED': '会员已过期，请续费后继续使用',
  
  // 支付相关错误
  'PAYMENT_FAILED': '支付失败，请重试或联系客服',
  'ORDER_NOT_FOUND': '订单不存在或已过期',
  'PAYMENT_TIMEOUT': '支付超时，请重新发起支付',
  'INVALID_AMOUNT': '支付金额异常，请联系客服',
  
  // 验证错误
  'INVALID_INPUT': '输入信息有误，请检查后重试',
  'MISSING_REQUIRED_FIELD': '请填写必填信息',
  'INVALID_FORMAT': '格式不正确，请重新输入',
  
  // 服务器错误
  'SERVER_ERROR': '服务器繁忙，请稍后重试',
  'DATABASE_ERROR': '数据处理失败，请稍后重试',
  'SERVICE_UNAVAILABLE': '服务暂时不可用，请稍后重试',
  
  // 默认错误
  'UNKNOWN_ERROR': '发生未知错误，请稍后重试',
};

/**
 * 创建应用错误对象
 */
export function createAppError(
  type: ErrorType,
  code: string,
  customMessage?: string,
  details?: any
): AppError {
  return {
    type,
    code,
    message: customMessage || ERROR_MESSAGES[code] || ERROR_MESSAGES['UNKNOWN_ERROR'],
    details,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 从HTTP响应创建错误
 */
export function createErrorFromResponse(response: Response, data?: any): AppError {
  let type = ErrorType.SERVER_ERROR;
  let code = 'SERVER_ERROR';
  
  switch (response.status) {
    case 401:
      type = ErrorType.AUTH_ERROR;
      code = 'UNAUTHORIZED';
      break;
    case 403:
      type = ErrorType.MEMBERSHIP_ERROR;
      code = data?.membershipRequired ? 'MEMBERSHIP_REQUIRED' : 'CHAT_LIMIT_EXCEEDED';
      break;
    case 404:
      type = ErrorType.VALIDATION_ERROR;
      code = 'ORDER_NOT_FOUND';
      break;
    case 429:
      type = ErrorType.VALIDATION_ERROR;
      code = 'RATE_LIMITED';
      break;
    case 500:
      type = ErrorType.SERVER_ERROR;
      code = 'SERVER_ERROR';
      break;
    default:
      type = ErrorType.UNKNOWN_ERROR;
      code = 'UNKNOWN_ERROR';
  }
  
  return createAppError(type, code, data?.error || data?.message, data);
}

/**
 * 从JavaScript错误创建应用错误
 */
export function createErrorFromException(error: Error): AppError {
  let type = ErrorType.UNKNOWN_ERROR;
  let code = 'UNKNOWN_ERROR';
  
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    type = ErrorType.NETWORK_ERROR;
    code = 'NETWORK_ERROR';
  } else if (error.message.includes('timeout')) {
    type = ErrorType.NETWORK_ERROR;
    code = 'TIMEOUT';
  }
  
  return createAppError(type, code, undefined, {
    originalError: error.message,
    stack: error.stack,
  });
}

/**
 * 错误日志记录
 */
export function logError(error: AppError, context?: string): void {
  const logData = {
    ...error,
    context,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  };
  
  console.error('[AppError]', logData);
  
  // 在生产环境中，这里可以发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 例如：发送到 Sentry, LogRocket 等
    // sendToErrorService(logData);
  }
}

/**
 * 错误重试机制
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      // 如果是最后一次重试，直接抛出错误
      if (i === maxRetries - 1) {
        break;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
  
  throw lastError!;
}

/**
 * 安全的API调用包装器
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<Response>,
  context?: string
): Promise<T> {
  try {
    const response = await apiCall();
    
    if (!response.ok) {
      const data = await response.json().catch(() => ({}));
      const error = createErrorFromResponse(response, data);
      logError(error, context);
      throw error;
    }
    
    return await response.json();
  } catch (error) {
    if (error instanceof Error && !(error as any).type) {
      // 如果不是AppError，转换为AppError
      const appError = createErrorFromException(error);
      logError(appError, context);
      throw appError;
    }
    
    throw error;
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyMessage(error: any): string {
  if (error?.type && error?.message) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return ERROR_MESSAGES[error] || error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return ERROR_MESSAGES['UNKNOWN_ERROR'];
}
