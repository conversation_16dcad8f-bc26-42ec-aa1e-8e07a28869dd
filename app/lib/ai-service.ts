import { generateText } from "ai";
import { aiConfig, validateAIConfig } from "@/app/lib/ai-config";
import { createResumePrompt } from "@/app/lib/prompts";
import { ResumeSchema } from "@/app/lib/types";

// Helper function to parse AI response and extract JSON
function parseAIResponse(
  response: string
): { resumeData: ResumeSchema; message: string } | null {
  try {
    // Enhanced regex to find JSON block, including those in markdown
    const jsonRegex =
      /```json\n([\s\S]*?)\n```|(?<=\s|^)({[\s\S]*}|\[[\s\S]*\])(?=\s|$)/;
    const match = response.match(jsonRegex);

    if (match) {
      // Prioritize the captured group from ```json ... ```, otherwise use the standalone JSON
      const jsonString = match[1] || match[2];
      if (jsonString) {
        const parsed = JSON.parse(jsonString);

        // The AI might return a full object { resumeData, message } or just a part of resumeData
        if (parsed.resumeData && parsed.message) {
          return parsed;
        } else {
          // This handles cases where the AI returns only a fragment of the resume data, e.g., { "experience": [...] }
          // We assume the message is a generic success message in this case.
          return {
            resumeData: parsed,
            message: "我已根据你的要求更新了简历。", // Generic success message
          };
        }
      }
    }

    console.error("No valid JSON found in AI response:", response);
    return null;
  } catch (error) {
    console.error("Failed to parse AI response:", response, error);
    return null;
  }
}

export async function generateResume(
  userInput: string,
  currentResume: ResumeSchema | null,
  locale: string = "zh"
) {
  // Validate AI configuration
  validateAIConfig();

  // Create the prompt for AI
  const prompt = createResumePrompt(userInput, currentResume, locale);

  // Use Vercel AI SDK to generate streaming response
  const result = await generateText({
    model: aiConfig.model,
    prompt,
    temperature: aiConfig.temperature,
    maxTokens: aiConfig.maxTokens,
  });

  // Parse the AI response
  const parsedResponse = parseAIResponse(result.text);

  if (parsedResponse) {
    return parsedResponse;
  } else {
    // Fallback if parsing fails
    return {
      resumeData: currentResume,
      message: "抱歉，我无法处理您的请求。请重试。",
    };
  }
}
