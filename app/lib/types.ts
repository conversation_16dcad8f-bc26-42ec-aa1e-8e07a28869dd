// This file defines the TypeScript types for our application's data structures.

export interface PersonalInfo {
  name: string;
  phone: string;
  email: string;
  linkedin: string;
}

export interface Experience {
  id: string;
  jobTitle: string;
  company: string;
  startDate: string;
  endDate: string;
  responsibilities: string[];
}

export interface Education {
  id: string;
  degree: string;
  major: string;
  school: string;
  startDate: string;
  graduationDate: string;
}

export interface SkillCategory {
  name: string; // 分类名称，如"技术技能"/"Technical Skills"
  data: string[]; // 该分类下的技能列表
}

export type Skills = SkillCategory[];

export interface Project {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  responsibilities: string[]; // 项目职责数组，替代原来的单一description字段
  technologies?: string[];
}

export interface ResumeSchema {
  personalInfo: PersonalInfo;
  summary: string;
  experience: Experience[];
  education: Education[];
  skills: Skills;
  projects: Project[];
}

// 扩展的简历数据结构，包含布局信息
export interface ExtendedResumeData {
  resumeData: ResumeSchema;
  sectionOrder: string[];
}
