// 支付方式枚举
export enum PaymentMethod {
  ALIPAY = "ALIPAY",
  WECHAT = "WECHAT", 
  STRIPE = "STRIPE",
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = "PENDING",
  SUCCESS = "SUCCESS", 
  FAILED = "FAILED",
  CANCELLED = "CANCELLED",
  REFUNDED = "REFUNDED",
}

// 订单创建参数
export interface CreateOrderParams {
  outTradeNo: string;
  totalAmount: string;
  subject: string;
  body?: string;
  returnUrl?: string;
  notifyUrl?: string;
  // 额外的支付方式特定参数
  extra?: Record<string, any>;
}

// 支付订单创建结果
export interface PaymentOrderResult {
  success: boolean;
  payUrl?: string;
  payData?: any; // 用于客户端SDK的支付数据
  outTradeNo: string;
  error?: string;
}

// 订单状态查询结果
export interface OrderStatusResult {
  success: boolean;
  status: PaymentStatus;
  tradeNo?: string; // 支付平台交易号
  paidAt?: Date;
  rawData?: any; // 原始响应数据
  error?: string;
}

// 回调验证结果
export interface CallbackVerifyResult {
  success: boolean;
  outTradeNo?: string;
  tradeNo?: string;
  status?: PaymentStatus;
  amount?: string;
  rawData?: any;
  error?: string;
}

// 支付配置基础接口
export interface PaymentConfig {
  enabled: boolean;
  sandbox?: boolean;
}

// 支付宝配置
export interface AlipayConfig extends PaymentConfig {
  appId: string;
  privateKey: string;
  alipayPublicKey: string;
  gateway: string;
}

// 微信支付配置
export interface WechatPayConfig extends PaymentConfig {
  appId: string;
  mchId: string;
  apiKey: string;
  certPath?: string;
  keyPath?: string;
  apiUrl: string;
}

// Stripe配置
export interface StripeConfig extends PaymentConfig {
  publishableKey: string;
  secretKey: string;
  webhookSecret: string;
  apiVersion?: string;
}

// 统一支付配置
export interface PaymentConfigs {
  alipay: AlipayConfig;
  wechat: WechatPayConfig;
  stripe: StripeConfig;
}
