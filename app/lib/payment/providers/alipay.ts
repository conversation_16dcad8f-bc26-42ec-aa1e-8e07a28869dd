import { AlipaySdk } from "alipay-sdk";
import { PaymentProvider } from "../provider";
import {
  PaymentMethod,
  CreateOrderParams,
  PaymentOrderResult,
  OrderStatusResult,
  CallbackVerifyResult,
  PaymentStatus,
  AlipayConfig,
} from "../types";

/**
 * 支付宝支付提供商
 */
export class AlipayProvider extends PaymentProvider {
  private sdk: AlipaySdk;
  private config: AlipayConfig;

  constructor(config: AlipayConfig) {
    super(PaymentMethod.ALIPAY);
    this.config = config;
    
    // 创建支付宝SDK实例
    this.sdk = new AlipaySdk({
      appId: config.appId,
      privateKey: config.privateKey,
      alipayPublicKey: config.alipayPublicKey,
      gateway: config.gateway,
    });
  }

  async createOrder(params: CreateOrderParams): Promise<PaymentOrderResult> {
    try {
      // 业务参数
      const bizContent = {
        out_trade_no: params.outTradeNo,
        total_amount: params.totalAmount,
        subject: params.subject,
        body: params.body || params.subject,
        product_code: "FAST_INSTANT_TRADE_PAY",
      };

      // 构建请求参数
      const requestParams: any = {
        bizContent,
      };

      // 设置回调地址
      if (params.returnUrl) {
        requestParams.returnUrl = params.returnUrl;
      }
      if (params.notifyUrl) {
        requestParams.notifyUrl = params.notifyUrl;
      }

      // 生成支付URL - 使用GET方法返回跳转链接
      const payUrl = this.sdk.pageExecute(
        "alipay.trade.page.pay",
        "GET",
        requestParams
      );

      return {
        success: true,
        payUrl: payUrl,
        outTradeNo: params.outTradeNo,
      };
    } catch (error) {
      console.error("创建支付宝订单失败:", error);
      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: error instanceof Error ? error.message : "创建订单失败",
      };
    }
  }

  async queryOrderStatus(outTradeNo: string): Promise<OrderStatusResult> {
    try {
      // 使用传统API方法查询订单状态
      const result = await this.sdk.exec("alipay.trade.query", {
        bizContent: {
          out_trade_no: outTradeNo,
        },
      });

      if (result && result.trade_status) {
        const { trade_status, trade_no } = result;
        
        let status: PaymentStatus;
        switch (trade_status) {
          case "TRADE_SUCCESS":
          case "TRADE_FINISHED":
            status = PaymentStatus.SUCCESS;
            break;
          case "TRADE_CLOSED":
            status = PaymentStatus.CANCELLED;
            break;
          case "WAIT_BUYER_PAY":
            status = PaymentStatus.PENDING;
            break;
          default:
            status = PaymentStatus.FAILED;
        }

        return {
          success: true,
          status,
          tradeNo: trade_no,
          paidAt: status === PaymentStatus.SUCCESS ? new Date() : undefined,
          rawData: result,
        };
      }

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: "查询结果异常",
      };
    } catch (error) {
      console.error("查询支付宝订单状态失败:", error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : "查询失败",
      };
    }
  }

  async verifyCallback(params: Record<string, any>): Promise<CallbackVerifyResult> {
    try {
      // 验证签名
      const isValid = this.sdk.checkNotifySign(params);
      
      if (!isValid) {
        return {
          success: false,
          error: "签名验证失败",
        };
      }

      // 解析回调数据
      const outTradeNo = params.out_trade_no;
      const tradeNo = params.trade_no;
      const tradeStatus = params.trade_status;
      const totalAmount = params.total_amount;

      let status: PaymentStatus;
      switch (tradeStatus) {
        case "TRADE_SUCCESS":
        case "TRADE_FINISHED":
          status = PaymentStatus.SUCCESS;
          break;
        case "TRADE_CLOSED":
          status = PaymentStatus.CANCELLED;
          break;
        default:
          status = PaymentStatus.FAILED;
      }

      return {
        success: true,
        outTradeNo,
        tradeNo,
        status,
        amount: totalAmount,
        rawData: params,
      };
    } catch (error) {
      console.error("验证支付宝回调失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    }
  }

  getDisplayName(): string {
    return "支付宝";
  }

  getIcon(): string {
    return "alipay";
  }

  isSupported(): boolean {
    return this.validateConfig();
  }

  getConfig(): AlipayConfig {
    return this.config;
  }

  validateConfig(): boolean {
    return !!(
      this.config.appId &&
      this.config.privateKey &&
      this.config.alipayPublicKey &&
      this.config.gateway
    );
  }
}

// 生成订单号的工具函数
export function generateOrderNo(): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `ORDER_${timestamp}_${random}`;
}
