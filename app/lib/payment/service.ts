import {
  PaymentMethod,
  CreateOrderParams,
  PaymentOrderResult,
  OrderStatusResult,
  CallbackVerifyResult,
} from "./types";
import { createPaymentProvider, getSupportedPaymentMethods } from "./factory";

/**
 * 统一支付服务类
 * 提供简化的支付操作接口
 */
export class PaymentService {
  private static instance: PaymentService;

  private constructor() {}

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  /**
   * 创建支付订单
   * @param method 支付方式
   * @param params 订单参数
   * @returns 支付订单结果
   */
  public async createOrder(
    method: PaymentMethod,
    params: CreateOrderParams
  ): Promise<PaymentOrderResult> {
    try {
      const provider = createPaymentProvider(method);
      return await provider.createOrder(params);
    } catch (error) {
      console.error(`创建${method}支付订单失败:`, error);
      return {
        success: false,
        outTradeNo: params.outTradeNo,
        error: error instanceof Error ? error.message : "创建订单失败",
      };
    }
  }

  /**
   * 查询订单状态
   * @param method 支付方式
   * @param outTradeNo 商户订单号
   * @returns 订单状态结果
   */
  public async queryOrderStatus(
    method: PaymentMethod,
    outTradeNo: string
  ): Promise<OrderStatusResult> {
    try {
      const provider = createPaymentProvider(method);
      return await provider.queryOrderStatus(outTradeNo);
    } catch (error) {
      console.error(`查询${method}订单状态失败:`, error);
      return {
        success: false,
        status: "FAILED" as any,
        error: error instanceof Error ? error.message : "查询失败",
      };
    }
  }

  /**
   * 验证支付回调
   * @param method 支付方式
   * @param params 回调参数
   * @returns 验证结果
   */
  public async verifyCallback(
    method: PaymentMethod,
    params: Record<string, any>
  ): Promise<CallbackVerifyResult> {
    try {
      const provider = createPaymentProvider(method);
      return await provider.verifyCallback(params);
    } catch (error) {
      console.error(`验证${method}支付回调失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    }
  }

  /**
   * 获取支持的支付方式
   * @returns 支付方式列表
   */
  public getSupportedMethods(): PaymentMethod[] {
    return getSupportedPaymentMethods();
  }

  /**
   * 检查支付方式是否支持
   * @param method 支付方式
   * @returns 是否支持
   */
  public isMethodSupported(method: PaymentMethod): boolean {
    try {
      createPaymentProvider(method);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取支付方式信息
   * @param method 支付方式
   * @returns 支付方式信息
   */
  public getMethodInfo(method: PaymentMethod): {
    displayName: string;
    icon: string;
    supported: boolean;
  } | null {
    try {
      const provider = createPaymentProvider(method);
      return {
        displayName: provider.getDisplayName(),
        icon: provider.getIcon(),
        supported: provider.isSupported(),
      };
    } catch {
      return null;
    }
  }

  /**
   * 获取所有支付方式信息
   * @returns 所有支付方式信息
   */
  public getAllMethodInfo(): Array<{
    method: PaymentMethod;
    displayName: string;
    icon: string;
    supported: boolean;
  }> {
    const methods = this.getSupportedMethods();
    return methods.map((method) => {
      const info = this.getMethodInfo(method);
      return {
        method,
        displayName: info?.displayName || method,
        icon: info?.icon || method.toLowerCase(),
        supported: info?.supported || false,
      };
    });
  }
}

// 导出单例实例
export const paymentService = PaymentService.getInstance();
