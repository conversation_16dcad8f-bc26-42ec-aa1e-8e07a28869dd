import { PaymentProvider, PaymentProviderFactory } from "./provider";
import { PaymentMethod } from "./types";
import { AlipayProvider } from "./providers/alipay";
// import { WechatPayProvider } from "./providers/wechat"; // 暂时禁用
import { StripeProvider } from "./providers/stripe";
import { paymentConfig } from "./config";

/**
 * 支付提供商工厂实现
 */
export class PaymentFactory implements PaymentProviderFactory {
  private static instance: PaymentFactory;
  private providers: Map<PaymentMethod, PaymentProvider> = new Map();

  private constructor() {
    this.initializeProviders();
  }

  public static getInstance(): PaymentFactory {
    if (!PaymentFactory.instance) {
      PaymentFactory.instance = new PaymentFactory();
    }
    return PaymentFactory.instance;
  }

  private initializeProviders(): void {
    // 初始化支付宝提供商
    if (paymentConfig.isPaymentMethodEnabled("alipay")) {
      try {
        const alipayProvider = new AlipayProvider(
          paymentConfig.getAlipayConfig()
        );
        if (alipayProvider.isSupported()) {
          this.providers.set(PaymentMethod.ALIPAY, alipayProvider);
        }
      } catch (error) {
        console.error("初始化支付宝提供商失败:", error);
      }
    }

    // 初始化微信支付提供商 (暂时禁用，等待SDK兼容性修复)
    // if (paymentConfig.isPaymentMethodEnabled("wechat")) {
    //   try {
    //     const wechatProvider = new WechatPayProvider(paymentConfig.getWechatConfig());
    //     if (wechatProvider.isSupported()) {
    //       this.providers.set(PaymentMethod.WECHAT, wechatProvider);
    //     }
    //   } catch (error) {
    //     console.error("初始化微信支付提供商失败:", error);
    //   }
    // }

    // 初始化Stripe提供商
    if (paymentConfig.isPaymentMethodEnabled("stripe")) {
      try {
        const stripeProvider = new StripeProvider(
          paymentConfig.getStripeConfig()
        );
        if (stripeProvider.isSupported()) {
          this.providers.set(PaymentMethod.STRIPE, stripeProvider);
        }
      } catch (error) {
        console.error("初始化Stripe提供商失败:", error);
      }
    }
  }

  public createProvider(method: PaymentMethod): PaymentProvider {
    const provider = this.providers.get(method);

    if (!provider) {
      throw new Error(`不支持的支付方式: ${method}`);
    }

    return provider;
  }

  public getSupportedMethods(): PaymentMethod[] {
    return Array.from(this.providers.keys());
  }

  public isMethodSupported(method: PaymentMethod): boolean {
    return this.providers.has(method);
  }

  public getProviderInfo(method: PaymentMethod): {
    displayName: string;
    icon: string;
    supported: boolean;
  } | null {
    const provider = this.providers.get(method);

    if (!provider) {
      return null;
    }

    return {
      displayName: provider.getDisplayName(),
      icon: provider.getIcon(),
      supported: provider.isSupported(),
    };
  }

  public getAllProviderInfo(): Array<{
    method: PaymentMethod;
    displayName: string;
    icon: string;
    supported: boolean;
  }> {
    return Array.from(this.providers.entries()).map(([method, provider]) => ({
      method,
      displayName: provider.getDisplayName(),
      icon: provider.getIcon(),
      supported: provider.isSupported(),
    }));
  }

  public refreshProviders(): void {
    this.providers.clear();
    this.initializeProviders();
  }
}

// 导出单例实例
export const paymentFactory = PaymentFactory.getInstance();

// 导出便捷方法
export function createPaymentProvider(method: PaymentMethod): PaymentProvider {
  return paymentFactory.createProvider(method);
}

export function getSupportedPaymentMethods(): PaymentMethod[] {
  return paymentFactory.getSupportedMethods();
}

export function isPaymentMethodSupported(method: PaymentMethod): boolean {
  return paymentFactory.isMethodSupported(method);
}

export function getPaymentMethodInfo(method: PaymentMethod) {
  return paymentFactory.getProviderInfo(method);
}

export function getAllPaymentMethodInfo() {
  return paymentFactory.getAllProviderInfo();
}
