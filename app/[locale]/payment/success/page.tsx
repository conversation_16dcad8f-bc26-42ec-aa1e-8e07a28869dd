"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Crown, ArrowLeft } from "lucide-react";

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [orderInfo, setOrderInfo] = useState<any>(null);

  const orderId = searchParams.get("orderId");

  useEffect(() => {
    if (orderId) {
      // 调用API验证订单状态
      const checkOrderStatus = async () => {
        try {
          const response = await fetch(`/api/orders/${orderId}/status`);
          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              setOrderInfo(result.data);
            } else {
              console.error("获取订单状态失败:", result.error);
            }
          } else {
            console.error("API请求失败:", response.status);
          }
        } catch (error) {
          console.error("检查订单状态失败:", error);
        } finally {
          setIsLoading(false);
        }
      };

      checkOrderStatus();
    } else {
      setIsLoading(false);
    }
  }, [orderId]);

  const handleBackToHome = () => {
    router.push("/");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            验证支付状态中...
          </p>
        </div>
      </div>
    );
  }

  if (!orderInfo) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-red-600 text-xl">✕</span>
              </div>
              <h2 className="text-lg font-semibold mb-2">支付信息异常</h2>
              <p className="text-sm text-muted-foreground mb-4">
                无法获取订单信息，请联系客服处理
              </p>
              <Button onClick={handleBackToHome} variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回首页
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-xl">支付成功！</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Crown className="w-5 h-5 text-yellow-500" />
              <span className="font-medium">会员已激活</span>
            </div>
            <p className="text-sm text-muted-foreground">
              恭喜您成为会员，现在可以享受无限制的AI简历服务
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">订单号</span>
              <span className="font-mono">{orderInfo.orderId}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">支付金额</span>
              <span className="font-medium">¥{orderInfo.amount}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">会员时长</span>
              <span className="font-medium">
                {orderInfo.product?.duration || 7}天
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">支付状态</span>
              <span className="font-medium text-green-600">
                {orderInfo.isPaid ? "已支付" : "待支付"}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <Button onClick={handleBackToHome} className="w-full">
              开始使用会员服务
            </Button>
            <p className="text-xs text-center text-muted-foreground">
              感谢您的支持，祝您使用愉快！
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
