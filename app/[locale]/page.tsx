"use client";

import { AppHeader } from "../components/app-header";
import { ChatArea } from "../components/chat-area";
import { ResumePreview } from "../components/resume-preview";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { useMembership } from "../hooks/useMembership";
import { MembershipDialog } from "../components/membership/membership-dialog";
import { useAppStore } from "../store";

export default function Home() {
  const { membershipStatus, refreshMembershipStatus } = useMembership();
  const { isMembershipDialogOpen, closeMembershipDialog } = useAppStore();

  const handlePaymentSuccess = () => {
    // 支付成功后强制刷新会员状态
    refreshMembershipStatus(true);
  };

  return (
    <div className="h-screen flex flex-col">
      <AppHeader />
      <main className="flex-grow overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          <ResizablePanel defaultSize={35} minSize={25}>
            <ChatArea />
          </ResizablePanel>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={65} minSize={40}>
            <ResumePreview />
          </ResizablePanel>
        </ResizablePanelGroup>
      </main>
      <MembershipDialog
        open={isMembershipDialogOpen}
        onOpenChange={(open) => !open && closeMembershipDialog()}
        onPaymentSuccess={handlePaymentSuccess}
        currentChatCount={membershipStatus?.chatCount}
        remainingChats={membershipStatus?.remainingChats}
      />
    </div>
  );
}
