"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  useMembership,
  usePurchaseMembership,
} from "../../hooks/useMembership";
import { MembershipDialog } from "../../components/membership/membership-dialog";

export default function TestMembershipPage() {
  const { data: session } = useSession();
  const { membershipStatus, isLoading, refreshMembershipStatus } =
    useMembership();
  const { purchaseMembership, isLoading: isPurchasing } =
    usePurchaseMembership();
  const [showDialog, setShowDialog] = useState(false);

  const handlePaymentSuccess = () => {
    refreshMembershipStatus();
  };

  const handleTestChat = async () => {
    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userInput: "测试聊天功能",
          resumeData: null,
          locale: "zh",
        }),
      });

      const data = await response.json();
      console.log("Chat response:", data);

      if (data.membershipRequired) {
        alert("需要会员权限！");
      } else {
        alert("聊天成功！");
      }

      // 刷新会员状态
      refreshMembershipStatus();
    } catch (error) {
      console.error("Chat test failed:", error);
    }
  };

  if (!session) {
    return (
      <div className="container mx-auto p-8">
        <Card>
          <CardContent className="pt-6">
            <p>请先登录以测试会员功能</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-2xl font-bold">会员功能测试页面</h1>

      <Card>
        <CardHeader>
          <CardTitle>用户信息</CardTitle>
        </CardHeader>
        <CardContent>
          <p>用户ID: {session.user.id}</p>
          <p>手机号: {session.user.phone}</p>
          <p>邮箱: {session.user.email}</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>会员状态</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>加载中...</p>
          ) : membershipStatus ? (
            <div className="space-y-2">
              <p>是否为会员: {membershipStatus.isMember ? "是" : "否"}</p>
              <p>聊天次数: {membershipStatus.chatCount}</p>
              <p>
                剩余次数:{" "}
                {membershipStatus.remainingChats === -1
                  ? "无限制"
                  : membershipStatus.remainingChats}
              </p>
              <p>可以聊天: {membershipStatus.canChat ? "是" : "否"}</p>
              {membershipStatus.memberExpiry && (
                <p>
                  会员到期时间:{" "}
                  {new Date(membershipStatus.memberExpiry).toLocaleString()}
                </p>
              )}
            </div>
          ) : (
            <p>无法获取会员状态</p>
          )}

          <div className="mt-4 space-x-2">
            <Button onClick={refreshMembershipStatus}>刷新状态</Button>
            <Button onClick={handleTestChat}>测试聊天</Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>会员购买测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-x-2">
            <Button onClick={() => setShowDialog(true)}>打开会员弹窗</Button>
          </div>
        </CardContent>
      </Card>

      <MembershipDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onPaymentSuccess={handlePaymentSuccess}
        currentChatCount={membershipStatus?.chatCount}
        remainingChats={membershipStatus?.remainingChats}
      />
    </div>
  );
}
