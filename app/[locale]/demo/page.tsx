"use client";

import React from "react";
import { EnhancedMessageContent } from "@/app/components/common/message-content";

const markdownContent = `# 标题 1

这是一个段落，包含**粗体文本**和*斜体文本*。

## 标题 2

### 代码示例

这是一个内联代码：\`console.log("Hello World")\`

\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet("World"));
\`\`\`

### 列表

#### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

#### 有序列表
1. 第一项
2. 第二项
3. 第三项

### 引用

> 这是一个引用块。
> 它可以包含多行内容。

### 表格

| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25   | 北京 |
| 李四 | 30   | 上海 |
| 王五 | 28   | 广州 |

### 链接

这是一个[链接示例](https://www.example.com)。

---

### 分割线

上面是一条分割线。
`;

export default function TestMarkdown() {
  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Markdown 渲染测试</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">启用 Markdown 渲染</h2>
          <div className="border border-gray-300 rounded-lg p-4 bg-white">
            <EnhancedMessageContent
              content={markdownContent}
              enableMarkdown={true}
              className="prose prose-sm max-w-none"
            />
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">纯文本模式</h2>
          <div className="border border-gray-300 rounded-lg p-4 bg-white">
            <EnhancedMessageContent
              content={markdownContent}
              enableMarkdown={false}
              className="text-gray-700"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
