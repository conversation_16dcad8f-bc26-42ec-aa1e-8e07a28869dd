import { useTranslations } from "next-intl";
import { useConfirmDialog } from "../providers/ConfirmProvider";

export function useErrorAlert() {
  const t = useTranslations("ErrorAlert");
  const { confirm } = useConfirmDialog();

  const showError = async (options: {
    title?: string;
    message: string;
  }) => {
    return confirm({
      title: options.title || t("title"),
      description: options.message,
      confirmText: t("confirm"),
      // 不显示取消按钮
      cancelText: undefined,
      variant: "default",
    });
  };

  return { showError };
}
