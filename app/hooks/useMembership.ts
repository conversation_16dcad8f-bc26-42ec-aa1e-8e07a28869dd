import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { MembershipStatus } from "@/app/lib/membership";
import { safeApiCall, getUserFriendlyMessage } from "@/app/lib/error-handler";

// 会员状态缓存
interface MembershipCache {
  data: MembershipStatus | null;
  timestamp: number;
  userId: string | null;
}

// 缓存过期时间（5分钟）
const CACHE_EXPIRY_TIME = 5 * 60 * 1000;

// 全局缓存对象
let membershipCache: MembershipCache = {
  data: null,
  timestamp: 0,
  userId: null,
};

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  type: string;
  sortOrder: number;
}

// 检查缓存是否有效
function isCacheValid(userId: string | null): boolean {
  const now = Date.now();
  return (
    membershipCache.userId === userId &&
    membershipCache.timestamp > 0 &&
    now - membershipCache.timestamp < CACHE_EXPIRY_TIME
  );
}

// 更新缓存
function updateCache(data: MembershipStatus | null, userId: string | null) {
  membershipCache = {
    data,
    timestamp: Date.now(),
    userId,
  };
}

export function useMembership() {
  const { data: session } = useSession();
  const [membershipStatus, setMembershipStatus] =
    useState<MembershipStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const forceRefreshRef = useRef(false);

  const fetchMembershipStatus = useCallback(
    async (forceRefresh = false) => {
      const userId = session?.user?.id || null;

      if (!session?.user) {
        setMembershipStatus(null);
        setIsLoading(false);
        return;
      }

      // 检查缓存是否有效（除非强制刷新）
      if (!forceRefresh && isCacheValid(userId)) {
        setMembershipStatus(membershipCache.data);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const data = await safeApiCall<{
          success: boolean;
          data: MembershipStatus;
        }>(() => fetch("/api/membership/status"), "fetchMembershipStatus");

        // 更新缓存和状态
        updateCache(data.data, userId);
        setMembershipStatus(data.data);
      } catch (err) {
        const errorMessage = getUserFriendlyMessage(err);
        setError(errorMessage);
        setMembershipStatus(null);
        // 清除无效缓存
        updateCache(null, userId);
      } finally {
        setIsLoading(false);
      }
    },
    [session]
  );

  useEffect(() => {
    fetchMembershipStatus();
  }, [fetchMembershipStatus]);

  const refreshMembershipStatus = useCallback(
    (forceRefresh = false) => {
      fetchMembershipStatus(forceRefresh);
    },
    [fetchMembershipStatus]
  );

  return {
    membershipStatus,
    isLoading,
    error,
    refreshMembershipStatus,
  };
}

export function usePurchaseMembership() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const purchaseMembership = async (
    productId: string,
    paymentMethod: string = "ALIPAY"
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await safeApiCall<{
        success: boolean;
        data: {
          payUrl?: string;
          payData?: any;
          orderId: string;
          outTradeNo: string;
          amount: number;
          paymentMethod: string;
        };
      }>(
        () =>
          fetch("/api/orders/create", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              productId: productId,
              paymentMethod: paymentMethod,
            }),
          }),
        "purchaseMembership"
      );

      if (data.success) {
        // 根据支付方式处理不同的支付流程
        if (data.data.payUrl) {
          // 支付宝和微信：在新标签页打开支付页面
          window.open(data.data.payUrl, "_blank");
        } else if (data.data.payData && data.data.paymentMethod === "STRIPE") {
          // Stripe：返回支付数据供客户端处理
          console.log("Stripe支付数据:", data.data.payData);
        }
        return data.data;
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (err) {
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    purchaseMembership,
    isLoading,
    error,
  };
}

// 获取商品列表的hook
export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await safeApiCall<{
        success: boolean;
        data: Product[];
      }>(() => fetch("/api/products?type=MEMBERSHIP"), "fetchProducts");

      setProducts(data.data);
    } catch (err) {
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    isLoading,
    error,
    refetch: fetchProducts,
  };
}
