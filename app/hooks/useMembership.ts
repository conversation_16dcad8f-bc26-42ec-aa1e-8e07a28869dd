import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { MembershipStatus } from "@/app/lib/membership";
import { safeApiCall, getUserFriendlyMessage } from "@/app/lib/error-handler";

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  type: string;
  sortOrder: number;
}

export function useMembership() {
  const { data: session } = useSession();
  const [membershipStatus, setMembershipStatus] =
    useState<MembershipStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMembershipStatus = useCallback(async () => {
    if (!session?.user) {
      setMembershipStatus(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const data = await safeApiCall<{
        success: boolean;
        data: MembershipStatus;
      }>(() => fetch("/api/membership/status"), "fetchMembershipStatus");

      setMembershipStatus(data.data);
    } catch (err) {
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);
      setMembershipStatus(null);
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  useEffect(() => {
    fetchMembershipStatus();
  }, [fetchMembershipStatus]);

  const refreshMembershipStatus = () => {
    fetchMembershipStatus();
  };

  return {
    membershipStatus,
    isLoading,
    error,
    refreshMembershipStatus,
  };
}



export function usePurchaseMembership() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const purchaseMembership = async (
    productId: string,
    paymentMethod: string = "ALIPAY"
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await safeApiCall<{
        success: boolean;
        data: {
          payUrl?: string;
          payData?: any;
          orderId: string;
          outTradeNo: string;
          amount: number;
          paymentMethod: string;
        };
      }>(
        () =>
          fetch("/api/orders/create", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              productId: productId,
              paymentMethod: paymentMethod,
            }),
          }),
        "purchaseMembership"
      );

      if (data.success) {
        // 根据支付方式处理不同的支付流程
        if (data.data.payUrl) {
          // 支付宝和微信：在新标签页打开支付页面
          window.open(data.data.payUrl, "_blank");
        } else if (data.data.payData && data.data.paymentMethod === "STRIPE") {
          // Stripe：返回支付数据供客户端处理
          console.log("Stripe支付数据:", data.data.payData);
        }
        return data.data;
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (err) {
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    purchaseMembership,
    isLoading,
    error,
  };
}

// 获取商品列表的hook
export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await safeApiCall<{
        success: boolean;
        data: Product[];
      }>(() => fetch("/api/products?type=MEMBERSHIP"), "fetchProducts");

      setProducts(data.data);
    } catch (err) {
      const errorMessage = getUserFriendlyMessage(err);
      setError(errorMessage);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    isLoading,
    error,
    refetch: fetchProducts,
  };
}
