import { useState, useCallback } from "react";

interface ConfirmOptions {
  title: string;
  description: string;
  confirmText: string;
  cancelText?: string;
  variant?: "default" | "destructive";
}

interface ConfirmState extends ConfirmOptions {
  open: boolean;
  resolve?: (value: boolean) => void;
}

export function useConfirm() {
  const [state, setState] = useState<ConfirmState>({
    open: false,
    title: "",
    description: "",
    confirmText: "",
  });

  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setState({
        ...options,
        open: true,
        resolve,
      });
    });
  }, []);

  const handleConfirm = useCallback(() => {
    state.resolve?.(true);
    setState(prev => ({ ...prev, open: false }));
  }, [state]);

  const handleCancel = useCallback(() => {
    state.resolve?.(false);
    setState(prev => ({ ...prev, open: false }));
  }, [state]);

  const handleOpenChange = useCallback((open: boolean) => {
    if (!open) {
      state.resolve?.(false);
    }
    setState(prev => ({ ...prev, open }));
  }, [state]);

  return {
    confirm,
    confirmProps: {
      open: state.open,
      onOpenChange: handleOpenChange,
      onConfirm: handleConfirm,
      onCancel: handleCancel,
      title: state.title,
      description: state.description,
      confirmText: state.confirmText,
      cancelText: state.cancelText,
      variant: state.variant,
    },
  };
}
