
import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useErrorAlert } from "./useErrorAlert";

export const useAuthForm = () => {
  const t = useTranslations("Auth");
  const [phone, setPhone] = useState("");
  const [code, setCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { showError } = useErrorAlert();

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      setCodeSent(false);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const handleSendCode = async () => {
    if (!phone.trim()) return;

    setIsSendingCode(true);
    try {
      const response = await fetch("/api/auth/send-sms", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ phone }),
      });

      if (response.ok) {
        setCodeSent(true);
        setCountdown(60);
      } else {
        const error = await response.json();
        showError({
          title: t("sendCodeError"),
          message: error.error || t("sendCodeError"),
        });
      }
    } catch (error) {
      showError({
        title: t("sendCodeError"),
        message: t("sendCodeError"),
      });
    } finally {
      setIsSendingCode(false);
    }
  };

  const handlePhoneLogin = async () => {
    if (!phone.trim() || !code.trim()) return;

    setIsLoading(true);
    try {
      const result = await signIn("phone", {
        phone,
        code,
        redirect: false,
      });

      if (!result?.ok) {
        showError({
          title: t("loginError"),
          message: t("loginError"),
        });
      }
      return result?.ok;
    } catch (error) {
      showError({
        title: t("loginError"),
        message: t("loginError"),
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setPhone("");
    setCode("");
    setCodeSent(false);
    setCountdown(0);
    setIsLoading(false);
    setIsSendingCode(false);
  };

  return {
    phone,
    setPhone,
    code,
    setCode,
    isLoading,
    isSendingCode,
    codeSent,
    countdown,
    handleSendCode,
    handlePhoneLogin,
    resetForm,
  };
};
