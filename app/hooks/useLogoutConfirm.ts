import { useTranslations } from "next-intl";
import { useConfirmDialog } from "../providers/ConfirmProvider";

export function useLogoutConfirm() {
  const t = useTranslations("LogoutConfirm");
  const { confirm } = useConfirmDialog();

  const confirmLogout = async () => {
    return confirm({
      title: t("title"),
      description: t("description"),
      confirmText: t("confirm"),
      cancelText: t("cancel"),
      variant: "default",
    });
  };

  return { confirmLogout };
}
