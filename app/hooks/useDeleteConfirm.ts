import { useTranslations } from "next-intl";
import { useConfirmDialog } from "../providers/ConfirmProvider";

export function useDeleteConfirm() {
  const t = useTranslations("DeleteConfirm");
  const { confirm } = useConfirmDialog();

  const confirmDelete = async (options?: {
    title?: string;
    description?: string;
  }) => {
    return confirm({
      title: options?.title || t("title"),
      description: options?.description || t("description"),
      confirmText: t("confirm"),
      cancelText: t("cancel"),
      variant: "destructive",
    });
  };

  return { confirmDelete };
}
