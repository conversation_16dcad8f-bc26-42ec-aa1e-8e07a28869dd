"use client";

import { useAppStore } from "@/app/store";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { useSession } from "next-auth/react";
import { useMembership } from "@/app/hooks/useMembership";
import { ChatHeader } from "./chat/ChatHeader";
import { MessageList } from "./chat/MessageList";
import { ChatInput } from "./chat/ChatInput";
import {
  isDefaultResumeData,
  syncResumeDataToServer,
} from "@/app/lib/resume-utils";

export function ChatArea() {
  const t = useTranslations("ChatArea");
  const locale = useLocale();
  const { data: session } = useSession();
  const {
    addMessage,
    resumeData,
    setResumeData,
    setIsGenerating,
    setLoginDialogOpen,
    loadChat,
    currentChatId,
  } = useAppStore();

  const { membershipStatus, refreshMembershipStatus } = useMembership();

  const { openMembershipDialog } = useAppStore();

  useEffect(() => {
    const { messages } = useAppStore.getState();
    if (messages.length === 0) {
      addMessage({ role: "assistant", content: t("initialMessage") });
    }
  }, [addMessage, t]);

  const handleSendMessage = async (
    userInput: string,
    referencedChats?: string[]
  ) => {
    if (!session?.user) {
      setLoginDialogOpen(true);
      return;
    }

    if (membershipStatus && !membershipStatus.canChat) {
      openMembershipDialog();
      return;
    }

    // 获取当前对话状态
    const {
      currentChatId,
      createNewChat,
      currentMessages,
      chatList,
      resumeData: currentResumeData,
    } = useAppStore.getState();

    let chatId = currentChatId;

    // 如果没有当前对话，创建新对话
    if (!chatId) {
      chatId = await createNewChat("新对话", currentResumeData || undefined);
      if (!chatId) {
        addMessage({ role: "assistant", content: "创建对话失败，请重试" });
        return;
      }
    }

    setIsGenerating(true);

    try {
      // 构建对话历史
      const conversationHistory = currentMessages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      // 获取引用对话的简历数据
      const referencedResumeData: any[] = [];
      if (referencedChats && referencedChats.length > 0) {
        for (const refChatId of referencedChats) {
          const refChat = chatList.find((chat) => chat.id === refChatId);
          if (refChat && refChat.resumeData) {
            referencedResumeData.push(refChat.resumeData);
          }
        }
      }

      // 判断是否为默认数据，如果是则不发送
      const shouldSendResumeData = !isDefaultResumeData(currentResumeData);

      const response = await fetch("/api/chat/conversation", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          chatId,
          userInput,
          locale,
          conversationHistory,
          currentResumeData: shouldSendResumeData ? currentResumeData : null,
          referencedResumeData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.membershipRequired) {
          openMembershipDialog();
          return;
        }
        throw new Error("API request failed");
      }

      const { message, resumeData: updatedResumeData } = await response.json();

      // 如果有简历数据更新，更新全局状态
      if (updatedResumeData) {
        setResumeData(updatedResumeData);
      }

      // 重新加载当前对话以获取最新的消息（包括用户消息和AI回复）
      if (chatId) {
        await loadChat(chatId);
      } else {
        // 如果没有对话ID，添加消息到全局状态
        addMessage({ role: "user", content: userInput });
        addMessage({ role: "assistant", content: message });
      }

      // 对话结束后同步简历数据到服务端
      const finalResumeData = updatedResumeData || currentResumeData;
      if (chatId && finalResumeData && !isDefaultResumeData(finalResumeData)) {
        await syncResumeDataToServer(chatId, finalResumeData);
      }

      refreshMembershipStatus();
    } catch (error) {
      console.error("Failed to send message:", error);
      addMessage({ role: "assistant", content: t("errorMessage") });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <ChatHeader />
      <MessageList />
      <ChatInput
        isGenerating={useAppStore((s) => s.isGenerating)}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
}
