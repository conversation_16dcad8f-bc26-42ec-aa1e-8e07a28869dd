
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { Product } from "@/app/hooks/useMembership";

interface ProductSelectorProps {
  products: Product[];
  selectedProduct: Product | null;
  setSelectedProduct: (product: Product) => void;
  isLoading: boolean;
}

export const ProductSelector = ({
  products,
  selectedProduct,
  setSelectedProduct,
  isLoading,
}: ProductSelectorProps) => {
  const t = useTranslations("Membership");

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm">{t("selectPackage")}</h3>
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
        </div>
      ) : (
        <div className="space-y-4">
          <div
            className={`
              ${
                products.length === 1
                  ? "flex justify-center"
                  : "flex gap-3 overflow-x-auto pb-2 hide-scrollbar"
              }
            `}
          >
            {products.map((product) => (
              <Card
                key={product.id}
                className={`
                    cursor-pointer transition-all duration-200 relative flex-shrink-0 border-2
                    ${
                      products.length === 1 ? "w-full max-w-sm" : "w-48"
                    }
                    ${
                      selectedProduct?.id === product.id
                        ? "border-blue-500  shadow-md bg-blue-50"
                        : "border-gray-200 hover:border-blue-300"
                    }
                  `}
                onClick={() => setSelectedProduct(product)}
              >
                <CardContent className="p-4">
                  <div className="text-center space-y-3">
                    <div>
                      <h4 className="font-bold text-lg">{product.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {t("averagePerDay")} ¥
                        {(Number(product.price) / product.duration).toFixed(1)}
                        {t("perDay")}
                      </p>
                    </div>

                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        ¥{product.price}
                      </div>
                    </div>

                    <p className="text-xs text-muted-foreground">
                      {product.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
