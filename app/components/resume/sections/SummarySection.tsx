"use client";

import { EditableField } from "@/app/components/common/EditableField";
import { SectionWrapper } from "@/app/components/common/SectionWrapper";
import { useTranslations } from "next-intl";

export const SummarySection = ({
  summary,
  isFirst,
  isLast,
}: {
  summary: string;
  isFirst: boolean;
  isLast: boolean;
}) => {
  const t = useTranslations("ResumePreview");

  if (!summary || summary === "") {
    return null;
  }

  return (
    <SectionWrapper
      sectionName="summary"
      canMoveUp={!isFirst}
      canMoveDown={!isLast}
    >
      <div className="summary-section">
        <h2 className="section-title">{t("summary")}</h2>
        <EditableField
          path="summary"
          value={summary}
          isTextArea={true}
          className="resume-content-text w-full"
        />
      </div>
    </SectionWrapper>
  );
};
