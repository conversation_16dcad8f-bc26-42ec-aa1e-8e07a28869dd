"use client";

import { ArrayItemWrapper } from "@/app/components/common/ArrayItemWrapper";
import { EditableField } from "@/app/components/common/EditableField";
import { SectionWrapper } from "@/app/components/common/SectionWrapper";
import { ResumeSchema } from "@/app/lib/types";
import { useTranslations } from "next-intl";

export const EducationSection = ({
  education,
  isFirst,
  isLast,
}: {
  education: ResumeSchema["education"];
  isFirst: boolean;
  isLast: boolean;
}) => {
  const t = useTranslations("ResumePreview");

  if (!education || education.length === 0) {
    return null;
  }

  return (
    <SectionWrapper
      sectionName="education"
      canMoveUp={!isFirst}
      canMoveDown={!isLast}
    >
      <div>
        <h2 className="section-title">{t("education")}</h2>
        {education.map((edu, index) => (
          <ArrayItemWrapper
            key={edu.id}
            arrayPath="education"
            index={index}
            totalItems={education.length}
            className="education-item flex justify-between items-baseline mb-2"
          >
            <div>
              <EditableField
                path={`education[${index}].school`}
                value={edu.school}
                className="font-semibold text-base print:text-sm"
              />
              <div className="flex items-center resume-secondary-text">
                <EditableField
                  path={`education[${index}].degree`}
                  value={edu.degree}
                />
                <span className="mx-1">,</span>
                <EditableField
                  path={`education[${index}].major`}
                  value={edu.major}
                />
              </div>
            </div>
            <div className="flex items-center resume-meta-text">
              <EditableField
                path={`education[${index}].startDate`}
                value={edu.startDate}
                className="resume-meta-text"
              />
              <span className="mx-1">-</span>
              <EditableField
                path={`education[${index}].graduationDate`}
                value={edu.graduationDate}
                className="resume-meta-text"
              />
            </div>
          </ArrayItemWrapper>
        ))}
      </div>
    </SectionWrapper>
  );
};
