"use client";

import { ArrayItemWrapper } from "@/app/components/common/ArrayItemWrapper";
import { EditableField } from "@/app/components/common/EditableField";
import { SectionWrapper } from "@/app/components/common/SectionWrapper";
import { TagItemWrapper } from "@/app/components/common/TagItemWrapper"; // Import the new component
import { ResumeSchema } from "@/app/lib/types";
import { useTranslations } from "next-intl";

export const ProjectsSection = ({
  projects,
  isFirst,
  isLast,
}: {
  projects: ResumeSchema["projects"];
  isFirst: boolean;
  isLast: boolean;
}) => {
  const t = useTranslations("ResumePreview");

  if (!projects || projects.length === 0) {
    return null;
  }

  return (
    <SectionWrapper
      sectionName="projects"
      canMoveUp={!isFirst}
      canMoveDown={!isLast}
    >
      <div>
        <h2 className="section-title">{t("projects")}</h2>
        <div className="space-y-4 print:space-y-3">
          {projects.map((project: any, index: number) => (
            <ArrayItemWrapper
              key={project.id}
              arrayPath="projects"
              index={index}
              totalItems={projects.length}
              className="project-item"
            >
              <div>
                <div className="flex justify-between items-baseline">
                  <EditableField
                    path={`projects[${index}].name`}
                    value={project.name}
                    className="font-semibold text-base print:text-sm"
                  />
                  <div className="flex items-center resume-meta-text">
                    <EditableField
                      path={`projects[${index}].startDate`}
                      value={project.startDate}
                    />
                    <span className="mx-1">-</span>
                    <EditableField
                      path={`projects[${index}].endDate`}
                      value={project.endDate}
                    />
                  </div>
                </div>
                {project.responsibilities &&
                  project.responsibilities.length > 0 && (
                    <ul className="list-disc list-outside pl-5 mt-2 print:mt-1 resume-list-text space-y-1">
                      {project.responsibilities.map(
                        (responsibility: string, respIndex: number) => (
                          <ArrayItemWrapper
                            key={respIndex}
                            arrayPath={`projects[${index}].responsibilities`}
                            index={respIndex}
                            totalItems={project.responsibilities.length}
                          >
                            <li className="w-full">
                              <EditableField
                                path={`projects[${index}].responsibilities[${respIndex}]`}
                                value={responsibility}
                                isTextArea={true}
                                className="w-full"
                              />
                            </li>
                          </ArrayItemWrapper>
                        )
                      )}
                    </ul>
                  )}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="mt-2">
                    <div className="relative flex flex-wrap gap-1">
                      {project.technologies.map((tech: string, i: number) => (
                        <TagItemWrapper
                          key={i}
                          arrayPath={`projects[${index}].technologies`}
                          index={i}
                          totalItems={project.technologies.length}
                        >
                          <EditableField
                            path={`projects[${index}].technologies[${i}]`}
                            value={tech}
                            className="bg-muted text-foreground/75 px-2 py-1 rounded-md text-xs print:text-[8pt]"
                          />
                        </TagItemWrapper>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </ArrayItemWrapper>
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};
