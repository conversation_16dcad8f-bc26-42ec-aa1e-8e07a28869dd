"use client";

import { ArrayItemWrapper } from "@/app/components/common/ArrayItemWrapper";
import { EditableField } from "@/app/components/common/EditableField";
import { SectionWrapper } from "@/app/components/common/SectionWrapper";
import { TagItemWrapper } from "@/app/components/common/TagItemWrapper";
import { ResumeSchema } from "@/app/lib/types";
import { useTranslations } from "next-intl";

export const SkillsSection = ({
  skills,
  isFirst,
  isLast,
}: {
  skills: ResumeSchema["skills"];
  isFirst: boolean;
  isLast: boolean;
}) => {
  const t = useTranslations("ResumePreview");

  if (!skills || skills.length === 0) {
    return null;
  }

  return (
    <SectionWrapper
      sectionName="skills"
      canMoveUp={!isFirst}
      canMoveDown={!isLast}
    >
      <div>
        <h2 className="section-title">{t("skills")}</h2>
        {skills.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4 print:gap-3">
            {skills.map((skillCategory: any, categoryIndex: number) => (
              <ArrayItemWrapper
                key={categoryIndex}
                arrayPath="skills"
                index={categoryIndex}
                totalItems={skills.length}
                className="skill-category"
              >
                <div>
                  <h3 className="font-semibold mb-2 text-base print:text-sm">
                    <EditableField
                      path={`skills[${categoryIndex}].name`}
                      value={skillCategory.name}
                      className="inline"
                    />
                  </h3>
                  <div className="relative flex flex-wrap gap-2">
                    {skillCategory.data.map(
                      (skill: string, skillIndex: number) => (
                        <TagItemWrapper
                          key={skillIndex}
                          arrayPath={`skills[${categoryIndex}].data`}
                          index={skillIndex}
                          totalItems={skillCategory.data.length}
                        >
                          <EditableField
                            path={`skills[${categoryIndex}].data[${skillIndex}]`}
                            value={skill}
                            className="bg-muted text-foreground/75 px-2 py-1 rounded-md text-xs print:text-[8pt]"
                          />
                        </TagItemWrapper>
                      )
                    )}
                  </div>
                </div>
              </ArrayItemWrapper>
            ))}
          </div>
        ) : (
          <div className="text-muted-foreground text-sm">{t("noSkills")}</div>
        )}
      </div>
    </SectionWrapper>
  );
};
