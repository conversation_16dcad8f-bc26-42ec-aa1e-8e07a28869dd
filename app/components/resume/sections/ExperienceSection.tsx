"use client";

import { ArrayItemWrapper } from "@/app/components/common/ArrayItemWrapper";
import { EditableField } from "@/app/components/common/EditableField";
import { SectionWrapper } from "@/app/components/common/SectionWrapper";
import { ResumeSchema } from "@/app/lib/types";
import { useTranslations } from "next-intl";

export const ExperienceSection = ({
  experience,
  isFirst,
  isLast,
}: {
  experience: ResumeSchema["experience"];
  isFirst: boolean;
  isLast: boolean;
}) => {
  const t = useTranslations("ResumePreview");

  if (!experience || experience.length === 0) {
    return null;
  }

  return (
    <SectionWrapper
      sectionName="experience"
      canMoveUp={!isFirst}
      canMoveDown={!isLast}
    >
      <div>
        <h2 className="section-title">{t("workExperience")}</h2>
        <div className="space-y-4 print:space-y-3">
          {experience.map((exp, index) => (
            <ArrayItemWrapper
              key={exp.id}
              arrayPath="experience"
              index={index}
              totalItems={experience.length}
              className="experience-item"
            >
              <div>
                <div className="flex justify-between items-baseline">
                  <EditableField
                    path={`experience[${index}].jobTitle`}
                    value={exp.jobTitle}
                    className="font-semibold text-base print:text-sm"
                  />
                  <div className="flex items-center resume-meta-text">
                    <EditableField
                      path={`experience[${index}].startDate`}
                      value={exp.startDate}
                    />
                    <span className="mx-1">-</span>
                    <EditableField
                      path={`experience[${index}].endDate`}
                      value={exp.endDate}
                    />
                  </div>
                </div>
                <EditableField
                  path={`experience[${index}].company`}
                  value={exp.company}
                  className="resume-secondary-text w-full"
                />
                <ul className="list-disc list-outside pl-5 mt-2 print:mt-1 resume-list-text space-y-1">
                  {exp.responsibilities.map((resp, i) => (
                    <ArrayItemWrapper
                      key={i}
                      arrayPath={`experience[${index}].responsibilities`}
                      index={i}
                      totalItems={exp.responsibilities.length}
                    >
                      <li className="w-full">
                        <EditableField
                          path={`experience[${index}].responsibilities[${i}]`}
                          value={resp}
                          isTextArea={true}
                          className="w-full"
                        />
                      </li>
                    </ArrayItemWrapper>
                  ))}
                </ul>
              </div>
            </ArrayItemWrapper>
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};
