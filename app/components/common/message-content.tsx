import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import "highlight.js/styles/github.css";

interface MessageContentProps {
  content: string;
  className?: string;
}

/**
 * MessageContent component handles proper line break parsing and text formatting
 * for chat messages and other text content that needs to preserve line breaks.
 */
export function MessageContent({
  content,
  className = "",
}: MessageContentProps) {
  // Handle different types of line breaks and preserve formatting
  const processContent = (text: string) => {
    if (!text) return null;

    // Split by various line break patterns (\n, \r\n, \r)
    const lines = text.split(/\r?\n/);

    return lines.map((line, index) => {
      // Handle empty lines - preserve them with proper spacing
      if (line.trim() === "") {
        return (
          <div key={index} className="h-5 leading-5">
            {"\u00A0"} {/* Non-breaking space for empty lines */}
          </div>
        );
      }

      // Regular line with content
      return (
        <div key={index} className="leading-5">
          {line}
        </div>
      );
    });
  };

  return (
    <div className={`text-sm ${className}`}>{processContent(content)}</div>
  );
}

/**
 * Enhanced MessageContent component with full markdown support
 */
export function EnhancedMessageContent({
  content,
  className = "",
  preserveWhitespace = false,
  enableMarkdown = false,
}: MessageContentProps & {
  preserveWhitespace?: boolean;
  enableMarkdown?: boolean;
}) {
  // If markdown is enabled, use ReactMarkdown for full markdown support
  if (enableMarkdown) {
    return (
      <div className={`text-sm ${className}`}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
          components={{
            // Custom styling for markdown elements
            h1: ({ children }) => (
              <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-xl font-bold mb-3 mt-5 first:mt-0">
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-lg font-bold mb-2 mt-4 first:mt-0">
                {children}
              </h3>
            ),
            h4: ({ children }) => (
              <h4 className="text-base font-bold mb-2 mt-3 first:mt-0">
                {children}
              </h4>
            ),
            h5: ({ children }) => (
              <h5 className="text-sm font-bold mb-2 mt-3 first:mt-0">
                {children}
              </h5>
            ),
            h6: ({ children }) => (
              <h6 className="text-xs font-bold mb-2 mt-3 first:mt-0">
                {children}
              </h6>
            ),
            p: ({ children }) => (
              <p className="mb-4 last:mb-0 leading-relaxed">{children}</p>
            ),
            ul: ({ children }) => (
              <ul className="list-disc list-inside mb-4 space-y-1">
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className="list-decimal list-inside mb-4 space-y-1">
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li className="leading-relaxed">{children}</li>
            ),
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-gray-300 pl-4 italic mb-4 text-gray-600">
                {children}
              </blockquote>
            ),
            code: ({ inline, children }: any) =>
              inline ? (
                <code className="bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              ) : (
                <code className="block bg-gray-100 p-3 rounded text-sm font-mono overflow-x-auto">
                  {children}
                </code>
              ),
            pre: ({ children }) => (
              <pre className="bg-gray-100 p-3 rounded mb-4 overflow-x-auto">
                {children}
              </pre>
            ),
            a: ({ href, children }) => (
              <a
                href={href}
                className="text-blue-600 hover:text-blue-800 underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                {children}
              </a>
            ),
            table: ({ children }) => (
              <div className="overflow-x-auto mb-4">
                <table className="min-w-full border-collapse border border-gray-300">
                  {children}
                </table>
              </div>
            ),
            thead: ({ children }) => (
              <thead className="bg-gray-50">{children}</thead>
            ),
            tbody: ({ children }) => <tbody>{children}</tbody>,
            tr: ({ children }) => (
              <tr className="border-b border-gray-300">{children}</tr>
            ),
            th: ({ children }) => (
              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-gray-300 px-4 py-2">{children}</td>
            ),
            hr: () => <hr className="border-gray-300 my-6" />,
            strong: ({ children }) => (
              <strong className="font-bold">{children}</strong>
            ),
            em: ({ children }) => <em className="italic">{children}</em>,
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  }

  // Fallback to original line-based processing for non-markdown content
  const processContent = (text: string) => {
    if (!text) return null;

    // Split by various line break patterns
    const lines = text.split(/\r?\n/);

    return lines.map((line, index) => {
      // Handle empty lines
      if (line.trim() === "") {
        return (
          <div key={index} className="h-5 leading-5">
            {"\u00A0"}
          </div>
        );
      }

      // Process line content based on options
      let processedLine = line;

      // Preserve whitespace if enabled
      if (preserveWhitespace) {
        processedLine = line.replace(/ /g, "\u00A0");
      }

      return (
        <div key={index} className="leading-5">
          {processedLine}
        </div>
      );
    });
  };

  return (
    <div
      className={`text-sm ${className} ${
        preserveWhitespace ? "font-mono" : ""
      }`}
    >
      {processContent(content)}
    </div>
  );
}

export default MessageContent;
