"use client";

import { useState } from "react";
import { ChevronDown, MessageCircle, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppStore } from "@/app/store";
import { Chat } from "@/app/store/types";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useLocale } from "next-intl";

export const ChatSelector = () => {
  const locale = useLocale();
  const {
    currentChat,
    chatList,
    isLoadingChats,
    loadChat,
    deleteChat,
  } = useAppStore();

  const [isOpen, setIsOpen] = useState(false);

  const handleSelectChat = async (chat: Chat) => {
    if (chat.id !== currentChat?.id) {
      await loadChat(chat.id);
    }
    setIsOpen(false);
  };

  const handleDeleteChat = async (e: React.MouseEvent, chatId: string) => {
    e.stopPropagation();
    if (confirm("确定要删除这个对话吗？")) {
      await deleteChat(chatId);
    }
  };

  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: locale === "zh" ? zhCN : undefined,
      });
    } catch {
      return "";
    }
  };

  const displayTitle = currentChat?.title || "选择对话";
  const truncatedTitle = displayTitle.length > 20 
    ? displayTitle.substring(0, 20) + "..." 
    : displayTitle;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 px-3 justify-between min-w-[120px] max-w-[200px]"
          disabled={isLoadingChats}
        >
          <span className="truncate">{truncatedTitle}</span>
          <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[300px]">
        {isLoadingChats ? (
          <DropdownMenuItem disabled>
            加载中...
          </DropdownMenuItem>
        ) : chatList.length === 0 ? (
          <DropdownMenuItem disabled>
            暂无对话
          </DropdownMenuItem>
        ) : (
          <>
            {chatList.map((chat) => (
              <DropdownMenuItem
                key={chat.id}
                className="flex items-center justify-between p-3 cursor-pointer"
                onClick={() => handleSelectChat(chat)}
              >
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <MessageCircle className="h-4 w-4 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">
                      {chat.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatTime(chat.updatedAt)}
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground"
                  onClick={(e) => handleDeleteChat(e, chat.id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
