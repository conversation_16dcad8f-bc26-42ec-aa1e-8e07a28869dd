"use client";

import { useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAppStore } from "@/app/store";
import { Message, ChatMessage } from "@/app/store/types";
import { Bot, User } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  MessageContent,
  EnhancedMessageContent,
} from "@/app/components/common/message-content";
import { TypingIndicator } from "./TypingIndicator";

// 转换ChatMessage到Message格式
const convertChatMessageToMessage = (chatMessage: ChatMessage): Message => ({
  role: chatMessage.role.toLowerCase() as "user" | "assistant",
  content: chatMessage.content,
});

export const MessageList = () => {
  const { messages, currentMessages, isGenerating, currentChatId } =
    useAppStore();

  // 如果有当前对话，使用当前对话的消息，否则使用全局消息
  const displayMessages: Message[] = currentChatId
    ? currentMessages.map(convertChatMessageToMessage)
    : messages;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [displayMessages]);

  return (
    <div className="flex-grow overflow-hidden">
      <ScrollArea className="h-full pr-1">
        <div className="space-y-4 py-4 px-6">
          {displayMessages.map((message: Message, index: number) => (
            <div
              key={index}
              className={`flex items-start gap-3 ${
                message.role === "user" ? "justify-end" : ""
              }`}
            >
              {/* {message.role === "assistant" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )} */}
              <div
                className={`rounded-lg px-3 py-2 max-w-[80%] ${
                  message.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                }`}
              >
                <EnhancedMessageContent
                  content={message.content}
                  enableMarkdown
                />
              </div>
              {/* {message.role === "user" && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )} */}
            </div>
          ))}
          {isGenerating && <TypingIndicator />}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    </div>
  );
};
