"use client";

import { useTranslations } from "next-intl";
import { MessageCircleCode, Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import { useMembership } from "@/app/hooks/useMembership";
import { Button } from "@/components/ui/button";
import { useAppStore } from "@/app/store";
import { ChatSelector } from "./ChatSelector";
import { useEffect } from "react";

export const ChatHeader = () => {
  const t = useTranslations("ChatArea");
  const { data: session } = useSession();
  const { membershipStatus } = useMembership();
  const {
    openMembershipDialog,
    loadChatList,
    createNewChat,
    resumeData,
    setLoginDialogOpen,
  } = useAppStore();

  // 组件挂载时加载对话列表
  useEffect(() => {
    if (session?.user) {
      loadChatList();
    }
  }, [session?.user, loadChatList]);

  const handleNewChat = async () => {
    if (!session?.user) {
      setLoginDialogOpen(true);
      return;
    }

    if (membershipStatus && !membershipStatus.canChat) {
      openMembershipDialog();
      return;
    }

    // 创建新对话，使用当前的简历数据作为初始数据
    await createNewChat("新对话", resumeData || undefined);
  };

  return (
    <div className="px-4 py-4.5 border-b">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <MessageCircleCode className="h-5 w-5" />
          <div className="flex items-center gap-2">
            {session?.user ? (
              <>
                <ChatSelector />
                <Button
                  onClick={handleNewChat}
                  variant="outline"
                  size="sm"
                  className="h-8"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <h2 className="text-lg font-semibold">{t("title")}</h2>
            )}
          </div>
        </div>

        {session?.user && membershipStatus && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {membershipStatus.isMember ? (
              <span className="text-green-600">会员</span>
            ) : (
              <span>剩余 {membershipStatus.remainingChats} 次免费对话</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
