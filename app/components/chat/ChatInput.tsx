"use client";

import { useState, KeyboardEvent, useRef, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { SendHorizontal, MessageCircle, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useAppStore } from "@/app/store";
import { Chat } from "@/app/store/types";
import { Badge } from "@/components/ui/badge";

interface ChatInputProps {
  isGenerating: boolean;
  onSendMessage: (message: string, referencedChats?: string[]) => void;
}

export const ChatInput = ({ isGenerating, onSendMessage }: ChatInputProps) => {
  const t = useTranslations("ChatArea");
  const { searchChatsForReference, chatList, currentChatId } = useAppStore();

  const [input, setInput] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<Chat[]>([]);
  const [referencedChats, setReferencedChats] = useState<Chat[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [cursorPosition, setCursorPosition] = useState(0);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 检测@符号并显示建议
  const handleInputChange = async (value: string) => {
    setInput(value);

    // 获取光标位置
    const textarea = textareaRef.current;
    if (!textarea) return;

    const cursorPos = textarea.selectionStart;
    setCursorPosition(cursorPos);

    // 检查是否在@符号后面
    const beforeCursor = value.substring(0, cursorPos);
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (atMatch) {
      const query = atMatch[1];
      setSearchQuery(query);
      setShowSuggestions(true);

      if (query.length > 0) {
        // 有搜索关键词时，搜索匹配的对话
        const results = await searchChatsForReference(query);
        setSuggestions(
          results.filter(
            (chat) =>
              chat.id !== currentChatId && // 排除当前对话
              !referencedChats.some((ref) => ref.id === chat.id) // 排除已引用的对话
          )
        );
      } else {
        // 空输入时，显示最近的对话（排除当前对话和已引用的对话）
        const recentChats = chatList
          .filter(
            (chat) =>
              chat.id !== currentChatId && // 排除当前对话
              !referencedChats.some((ref) => ref.id === chat.id) // 排除已引用的对话
          )
          .slice(0, 5); // 只显示最近5个对话
        setSuggestions(recentChats);
      }
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
      setSearchQuery("");
    }
  };

  // 选择引用的对话
  const handleSelectReference = (chat: Chat) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // 替换@查询为对话标题
    const beforeCursor = input.substring(0, cursorPosition);
    const afterCursor = input.substring(cursorPosition);
    const atMatch = beforeCursor.match(/@([^@\s]*)$/);

    if (atMatch) {
      const newBeforeCursor = beforeCursor.replace(
        /@([^@\s]*)$/,
        `@${chat.title} `
      );
      const newInput = newBeforeCursor + afterCursor;
      setInput(newInput);

      // 添加到引用列表
      setReferencedChats((prev) => [...prev, chat]);

      // 隐藏建议
      setShowSuggestions(false);
      setSuggestions([]);

      // 重新聚焦并设置光标位置
      setTimeout(() => {
        textarea.focus();
        const newCursorPos = newBeforeCursor.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    }
  };

  // 移除引用
  const handleRemoveReference = (chatId: string) => {
    setReferencedChats((prev) => prev.filter((chat) => chat.id !== chatId));

    // 从输入文本中移除对应的@引用
    const chatToRemove = referencedChats.find((chat) => chat.id === chatId);
    if (chatToRemove) {
      const newInput = input.replace(
        new RegExp(`@${chatToRemove.title}\\s?`, "g"),
        ""
      );
      setInput(newInput);
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions && suggestions.length > 0) {
      if (event.key === "Escape") {
        setShowSuggestions(false);
        return;
      }
      // 可以添加上下箭头选择逻辑
    }

    // 处理退格键删除@引用
    if (event.key === "Backspace") {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const cursorPos = textarea.selectionStart;
      const beforeCursor = input.substring(0, cursorPos);

      // 检查光标前是否有@引用
      const atReferenceMatch = beforeCursor.match(/@([^@\s]+)\s*$/);
      if (atReferenceMatch) {
        event.preventDefault();

        // 找到对应的引用对话
        const referenceName = atReferenceMatch[1];
        const referencedChat = referencedChats.find(
          (chat) => chat.title === referenceName
        );

        if (referencedChat) {
          // 删除整个@引用
          const beforeReference = beforeCursor.substring(
            0,
            beforeCursor.lastIndexOf("@")
          );
          const afterCursor = input.substring(cursorPos);
          const newInput = beforeReference + afterCursor;

          setInput(newInput);

          // 从引用列表中移除
          setReferencedChats((prev) =>
            prev.filter((chat) => chat.id !== referencedChat.id)
          );

          // 设置新的光标位置
          setTimeout(() => {
            textarea.setSelectionRange(
              beforeReference.length,
              beforeReference.length
            );
          }, 0);
        }
      }
    }

    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  };

  const handleSubmit = () => {
    if (input.trim() !== "") {
      const referencedChatIds = referencedChats.map((chat) => chat.id);
      onSendMessage(input, referencedChatIds);
      setInput("");
      setReferencedChats([]);
      setShowSuggestions(false);
    }
  };

  return (
    <div className="p-4 pt-2 flex flex-col items-start gap-2 border-t">
      {/* 引用的对话显示 */}
      {referencedChats.length > 0 && (
        <div className="w-full">
          <div className="flex flex-wrap gap-2 mb-2">
            {referencedChats.map((chat) => (
              <Badge
                key={chat.id}
                variant="secondary"
                className="flex items-center gap-1"
              >
                <MessageCircle className="h-3 w-3" />
                <span className="text-xs">{chat.title}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                  onClick={() => handleRemoveReference(chat.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      <div className="relative w-full">
        <Textarea
          ref={textareaRef}
          placeholder={t("placeholder")}
          value={input}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isGenerating}
          rows={2}
          className="resize-none pr-12 focus-visible:ring-1"
        />
        <Button
          type="submit"
          size="icon"
          className="absolute top-1/2 right-3 -translate-y-1/2 rounded-full w-8 h-8"
          onClick={handleSubmit}
          disabled={isGenerating || !input.trim()}
        >
          <SendHorizontal className="w-4 h-4" />
        </Button>

        {/* 对话建议下拉框 */}
        {showSuggestions && (
          <div className="absolute bottom-full left-0 right-0 mb-1 bg-background border rounded-md shadow-lg max-h-48 overflow-y-auto z-50">
            {suggestions.length === 0 ? (
              <div className="p-3 text-sm text-muted-foreground">
                {searchQuery ? "未找到匹配的对话" : "输入关键词搜索对话"}
              </div>
            ) : (
              suggestions.map((chat) => (
                <div
                  key={chat.id}
                  className="flex items-center gap-2 p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                  onClick={() => handleSelectReference(chat)}
                >
                  <MessageCircle className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {chat.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(chat.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>

      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            使用 @ 引用其他对话
          </span>
        </div>
        <p className="text-xs text-muted-foreground">{t("enterToSend")}</p>
      </div>
    </div>
  );
};
