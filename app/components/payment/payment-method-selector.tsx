"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  Smartphone,
  Banknote,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

export interface PaymentMethodInfo {
  method: string;
  displayName: string;
  icon: string;
  supported: boolean;
}

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onMethodChange: (method: string) => void;
  className?: string;
}

// 支付方式图标映射
const PaymentIcons = {
  alipay: Banknote,
  wechat: Smartphone,
  stripe: CreditCard,
};

// 支付方式颜色映射
const PaymentColors = {
  alipay: "bg-blue-50 border-blue-200 text-blue-700",
  wechat: "bg-green-50 border-green-200 text-green-700",
  stripe: "bg-purple-50 border-purple-200 text-purple-700",
};

export function PaymentMethodSelector({
  selectedMethod,
  onMethodChange,
  className = "",
}: PaymentMethodSelectorProps) {
  const t = useTranslations("PaymentMethod");
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodInfo[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchPaymentMethods = useCallback(async () => {
    try {
      const response = await fetch("/api/payment/methods");
      const result = await response.json();

      if (result.success) {
        setPaymentMethods(result.data);
        // 如果当前没有选中的支付方式，自动选择第一个可用的
        if (result.data.length > 0 && !selectedMethod) {
          onMethodChange(result.data[0].method);
        }
      }
    } catch (error) {
      console.error("获取支付方式失败:", error);
      // 设置默认支付方式
      const defaultMethods = [
        {
          method: "ALIPAY",
          displayName: "支付宝",
          icon: "alipay",
          supported: true,
        },
      ];
      setPaymentMethods(defaultMethods);
      // 自动选择默认支付方式
      if (!selectedMethod) {
        onMethodChange(defaultMethods[0].method);
      }
    } finally {
      setLoading(false);
    }
  }, [onMethodChange, selectedMethod]);

  useEffect(() => {
    fetchPaymentMethods();
  }, [fetchPaymentMethods]);

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <h3 className="text-sm font-medium text-gray-900">
          {t("selectPaymentMethod")}
        </h3>
        <div className="animate-pulse space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  const supportedMethods = paymentMethods.filter((method) => method.supported);

  if (supportedMethods.length === 0) {
    return (
      <div className={`space-y-3 ${className}`}>
        <h3 className="text-sm font-medium text-gray-900">
          {t("selectPaymentMethod")}
        </h3>
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="flex items-center gap-2 p-4">
            <AlertCircle className="w-5 h-5 text-orange-600" />
            <span className="text-sm text-orange-700">
              {t("noPaymentMethods")}
            </span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h3 className="text-sm font-medium text-gray-900">
        {t("selectPaymentMethod")}
      </h3>

      <RadioGroup
        value={selectedMethod}
        onValueChange={onMethodChange}
        className="space-y-2"
      >
        {supportedMethods.map((method) => {
          const IconComponent =
            PaymentIcons[method.icon as keyof typeof PaymentIcons] ||
            CreditCard;
          const colorClass =
            PaymentColors[method.icon as keyof typeof PaymentColors] ||
            "bg-gray-50 border-gray-200 text-gray-700";
          const isSelected = selectedMethod === method.method;

          return (
            <div key={method.method} className="relative">
              <Label htmlFor={method.method} className="cursor-pointer block">
                <Card
                  className={`
                  transition-all duration-200 hover:shadow-md
                  ${
                    isSelected
                      ? `ring-2 ring-blue-500 ${colorClass}`
                      : "border-gray-200 hover:border-gray-300"
                  }
                `}
                >
                  <CardContent className="flex items-center gap-3 p-4">
                    <RadioGroupItem
                      value={method.method}
                      id={method.method}
                      className="mt-0.5"
                    />

                    <div
                      className={`
                      flex items-center justify-center w-10 h-10 rounded-lg
                      ${isSelected ? colorClass : "bg-gray-100"}
                    `}
                    >
                      <IconComponent className="w-5 h-5" />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">
                          {method.displayName}
                        </span>
                        {method.method === "ALIPAY" && (
                          <Badge variant="secondary" className="text-xs">
                            {t("recommended")}
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {method.method === "ALIPAY" && t("alipayDesc")}
                        {method.method === "WECHAT" && t("wechatDesc")}
                        {method.method === "STRIPE" && t("stripeDesc")}
                      </p>
                    </div>

                    {isSelected && (
                      <CheckCircle className="w-5 h-5 text-blue-600" />
                    )}
                  </CardContent>
                </Card>
              </Label>
            </div>
          );
        })}
      </RadioGroup>

      {/* <p className="text-xs text-muted-foreground">{t("securityNote")}</p> */}
    </div>
  );
}
