import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { AppState } from "./types";
import { createAuthSlice } from "./slices/auth-slice";
import { createMessageSlice } from "./slices/message-slice";
import { createResumeDataSlice } from "./slices/resume-data-slice";
import { createResumeLayoutSlice } from "./slices/resume-layout-slice";
import { createMembershipSlice } from "./slices/membership-slice";
import { createChatSlice } from "./slices/chat-slice";
import { defaultResumeData } from "./defaults";

export const useAppStore = create(
  devtools(
    immer<AppState>((...a) => ({
      ...createAuthSlice(...a),
      ...createMessageSlice(...a),
      ...createResumeDataSlice(...a),
      ...createResumeLayoutSlice(...a),
      ...createMembershipSlice(...a),
      ...createChatSlice(...a),
      // Initialize with default resume data
      resumeData: defaultResumeData,
    }))
  )
);
