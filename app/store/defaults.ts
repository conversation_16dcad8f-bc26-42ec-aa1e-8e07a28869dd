
import { ResumeSchema } from '../lib/types';

export const defaultResumeData: ResumeSchema = {
  personalInfo: {
    name: "李明",
    phone: "138-8888-8888",
    email: "<EMAIL>",
    linkedin: "linkedin.com/in/liming",
  },
  summary:
    "一位拥有5年经验的资深软件工程师，专注于后端开发。对使用Java和Go构建可扩展、高性能的系统充满热情。目前正在上海寻求新的职业机会。",
  experience: [
    {
      id: "exp1",
      jobTitle: "高级软件工程师",
      company: "Tech Solutions Inc.",
      startDate: "2021-07",
      endDate: "至今",
      responsibilities: [
        "主导后端服务的设计与开发，将系统吞吐量提高了40%。",
        "使用Go语言重构了核心支付网关，减少了90%的延迟。",
        "指导初级工程师，并负责代码审查以确保代码质量。",
      ],
    },
    {
      id: "exp2",
      jobTitle: "软件工程师",
      company: "Web Innovators",
      startDate: "2018-03",
      endDate: "2021-06",
      responsibilities: [
        "开发和维护基于Java的RESTful API，为公司旗舰产品提供支持。",
        "与产品经理合作，将客户需求转化为技术解决方案。",
        "参与数据库设计和优化，将查询响应时间减少了200ms。",
      ],
    },
  ],
  education: [
    {
      id: "edu1",
      degree: "计算机科学与技术",
      major: "硕士",
      school: "上海交通大学",
      startDate: "2018-09",
      graduationDate: "2021-06",
    },
    {
      id: "edu2",
      degree: "计算机科学与技术",
      major: "学士",
      school: "上海交通大学",
      startDate: "2014-09",
      graduationDate: "2018-06",
    },
  ],
  skills: [
    {
      name: "技术技能",
      data: [
        "Java",
        "Go",
        "Spring Boot",
        "Gin",
        "MySQL",
        "PostgreSQL",
        "Docker",
        "Kubernetes",
        "Redis",
        "Kafka",
      ],
    },
    {
      name: "软技能",
      data: ["团队合作", "解决问题", "有效的沟通技巧", "领导力"],
    },
  ],
  projects: [
    {
      id: "proj1",
      name: "电商平台后端系统",
      startDate: "2022-03",
      endDate: "2023-01",
      responsibilities: [
        "负责核心支付模块和订单系统的设计与开发",
        "基于微服务架构实现高并发订单处理和实时库存管理",
        "使用Spring Boot和Redis实现缓存优化，系统性能提升60%",
        "参与系统架构设计，确保系统的可扩展性和稳定性",
      ],
      technologies: ["Java", "Spring Boot", "MySQL", "Redis", "Docker"],
    },
    {
      id: "proj2",
      name: "实时数据分析平台",
      startDate: "2023-02",
      endDate: "2023-08",
      responsibilities: [
        "负责数据采集、清洗和可视化模块的开发",
        "构建实时数据处理和分析平台，处理每日千万级数据流",
        "集成Kafka和Elasticsearch，为业务决策提供实时数据支持",
        "优化数据处理流程，提升数据处理效率和准确性",
      ],
      technologies: ["Go", "Kafka", "Elasticsearch", "Kubernetes", "Grafana"],
    },
  ],
};

export const emptyResumeData: ResumeSchema = {
  personalInfo: {
    name: "",
    phone: "",
    email: "",
    linkedin: "",
  },
  summary: "",
  experience: [],
  education: [],
  skills: [],
  projects: [],
};
