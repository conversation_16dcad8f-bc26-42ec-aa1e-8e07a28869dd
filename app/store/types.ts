import { ResumeSchema } from "../lib/types";
import { AuthSlice } from "./slices/auth-slice";
import { MessageSlice } from "./slices/message-slice";
import { ResumeDataSlice } from "./slices/resume-data-slice";
import { ResumeLayoutSlice } from "./slices/resume-layout-slice";
import { MembershipSlice } from "./slices/membership-slice";
import { ChatSlice } from "./slices/chat-slice";

// Define the message structure
export interface Message {
  role: "user" | "assistant";
  content: string;
  isStreaming?: boolean; // For streaming messages
}

// Define user interface
export interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  phone?: string | null;
  image?: string | null;
}

// Define resume metadata interface
export interface ResumeMetadata {
  id: string;
  name: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// Define chat message interface (aligned with database model)
export interface ChatMessage {
  id: string;
  chatId: string;
  role: "USER" | "ASSISTANT" | "SYSTEM";
  content: string;
  metadata?: any;
  createdAt: string;
}

// Define chat interface (aligned with database model)
export interface Chat {
  id: string;
  userId: string;
  title: string;
  resumeId?: string | null;
  resumeData?: ResumeSchema | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  messages?: ChatMessage[];
}

// The complete application state, combining all slices
export type AppState = AuthSlice &
  MessageSlice &
  ResumeDataSlice &
  ResumeLayoutSlice &
  MembershipSlice &
  ChatSlice;
