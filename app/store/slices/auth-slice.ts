import { StateCreator } from "zustand";
import { AppState, User } from "../types";

export interface AuthSlice {
  user: User | null;
  isLoginDialogOpen: boolean;
  setUser: (user: User | null) => void;
  setLoginDialogOpen: (open: boolean) => void;
}

export const createAuthSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  AuthSlice
> = (set) => ({
  user: null,
  isLoginDialogOpen: false,
  setUser: (user) =>
    set((state) => {
      state.user = user;
    }),
  setLoginDialogOpen: (open) =>
    set((state) => {
      state.isLoginDialogOpen = open;
    }),
});
