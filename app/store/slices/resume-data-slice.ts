import { StateCreator } from "zustand";
import { AppState } from "../types";
import { ResumeSchema } from "@/app/lib/types";

export interface ResumeDataSlice {
  resumeData: ResumeSchema | null;
  setResumeData: (data: ResumeSchema) => void;
  updateResumeField: (path: string, value: any) => void;
  deleteArrayItem: (arrayPath: string, index: number) => void;
  moveArrayItem: (
    arrayPath: string,
    fromIndex: number,
    toIndex: number
  ) => void;
  addResumeArrayField: (arrayPath: string, newItem: any) => void;
}

export const createResumeDataSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  ResumeDataSlice
> = (set) => ({
  resumeData: null, // Initialized in the main store
  setResumeData: (data) =>
    set((state) => {
      state.resumeData = data;
    }),
  updateResumeField: (path, value) =>
    set((state) => {
      if (!state.resumeData) return;

      const keys = path
        .replace(/[\[\]]/g, ".")
        .replace(/\.\./g, ".")
        .replace(/^\.|\.$/g, "")
        .split(".");
      let current: any = state.resumeData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current?.[keys[i]];
        if (current === undefined) {
          console.error(`Invalid path for resume update: ${path}`);
          return;
        }
      }

      if (current !== undefined) {
        current[keys[keys.length - 1]] = value;
      }
    }),
  deleteArrayItem: (arrayPath, index) =>
    set((state) => {
      if (!state.resumeData) return;

      const keys = arrayPath
        .replace(/[\[\]]/g, ".")
        .replace(/\.\./g, ".")
        .replace(/^\.|\.$/g, "")
        .split(".");
      let current: any = state.resumeData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current?.[keys[i]];
        if (current === undefined) return;
      }

      const arrayKey = keys[keys.length - 1];
      if (current && Array.isArray(current[arrayKey])) {
        current[arrayKey].splice(index, 1);
      }
    }),
  moveArrayItem: (arrayPath, fromIndex, toIndex) =>
    set((state) => {
      if (!state.resumeData) return;

      const keys = arrayPath
        .replace(/[\[\]]/g, ".")
        .replace(/\.\./g, ".")
        .replace(/^\.|\.$/g, "")
        .split(".");
      let current: any = state.resumeData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current?.[keys[i]];
        if (current === undefined) return;
      }

      const arrayKey = keys[keys.length - 1];
      if (current && Array.isArray(current[arrayKey])) {
        const array = current[arrayKey];
        if (
          fromIndex >= 0 &&
          fromIndex < array.length &&
          toIndex >= 0 &&
          toIndex < array.length
        ) {
          const [item] = array.splice(fromIndex, 1);
          array.splice(toIndex, 0, item);
        }
      }
    }),
  addResumeArrayField: (arrayPath, newItem) =>
    set((state) => {
      if (!state.resumeData) return;

      const keys = arrayPath
        .replace(/[\[\]]/g, ".")
        .replace(/\.\./g, ".")
        .replace(/^\.|\.$/g, "")
        .split(".");
      let current: any = state.resumeData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current?.[keys[i]];
        if (current === undefined) {
          console.error(`Invalid path for adding to array: ${arrayPath}`);
          return;
        }
      }

      const arrayKey = keys[keys.length - 1];
      if (current && Array.isArray(current[arrayKey])) {
        current[arrayKey].push(newItem);
      } else {
        console.error(`Target is not an array at path: ${arrayPath}`);
      }
    }),
});
