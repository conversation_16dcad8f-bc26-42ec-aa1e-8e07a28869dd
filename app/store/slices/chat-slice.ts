import { StateCreator } from "zustand";
import { AppState, Chat, ChatMessage } from "../types";
import { ResumeSchema } from "../../lib/types";
import { defaultResumeData } from "../defaults";
import { isDefaultResumeData } from "../../lib/resume-utils";

export interface ChatSlice {
  // 当前对话状态
  currentChatId: string | null;
  currentChat: Chat | null;
  currentMessages: ChatMessage[];

  // 对话列表
  chatList: Chat[];
  isLoadingChats: boolean;

  // 对话管理方法
  loadChatList: () => Promise<void>;
  loadChat: (chatId: string) => Promise<void>;
  createNewChat: (
    title?: string,
    resumeData?: ResumeSchema
  ) => Promise<string | null>;
  deleteChat: (chatId: string) => Promise<boolean>;

  // 引用功能
  searchChatsForReference: (query: string) => Promise<Chat[]>;
}

export const createChatSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  ChatSlice
> = (set, get) => ({
  // 初始状态
  currentChatId: null,
  currentChat: null,
  currentMessages: [],
  chatList: [],
  isLoadingChats: false,

  // 加载对话列表
  loadChatList: async () => {
    set((state) => {
      state.isLoadingChats = true;
    });

    try {
      const response = await fetch("/api/chats");
      if (!response.ok) {
        throw new Error("Failed to load chats");
      }

      const { data: chats } = await response.json();

      set((state) => {
        state.chatList = chats;
        state.isLoadingChats = false;
      });
    } catch (error) {
      console.error("Failed to load chat list:", error);
      set((state) => {
        state.isLoadingChats = false;
      });
    }
  },

  // 加载特定对话
  loadChat: async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`);
      if (!response.ok) {
        throw new Error("Failed to load chat");
      }

      const { data: chat } = await response.json();

      set((state) => {
        state.currentChatId = chatId;
        state.currentChat = chat;
        state.currentMessages = chat.messages || [];

        // 更新简历数据到全局状态
        if (chat.resumeData) {
          state.resumeData = chat.resumeData;
        }
      });
    } catch (error) {
      console.error("Failed to load chat:", error);
    }
  },

  // 创建新对话
  createNewChat: async (title = "新对话", resumeData) => {
    try {
      // 判断是否为默认数据，如果是则不发送到后端
      const shouldSendResumeData =
        resumeData && !isDefaultResumeData(resumeData);

      const response = await fetch("/api/chats", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title,
          resumeData: shouldSendResumeData ? resumeData : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create chat");
      }

      const { data: chat } = await response.json();

      set((state) => {
        state.chatList.unshift(chat);
        state.currentChatId = chat.id;
        state.currentChat = chat;
        state.currentMessages = [];

        // 新建对话时，如果没有传入简历数据，则使用默认简历数据
        // 这样可以确保resumePreview始终有数据显示
        if (resumeData) {
          state.resumeData = resumeData;
        } else {
          state.resumeData = defaultResumeData;
        }
      });

      return chat.id;
    } catch (error) {
      console.error("Failed to create chat:", error);
      return null;
    }
  },

  // 删除对话
  deleteChat: async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete chat");
      }

      set((state) => {
        const wasCurrentChat = state.currentChatId === chatId;

        // 从对话列表中移除
        state.chatList = state.chatList.filter((chat) => chat.id !== chatId);

        // 如果删除的是当前对话，尝试选择下一个对话
        if (wasCurrentChat) {
          if (state.chatList.length > 0) {
            // 选择第一个可用的对话
            const nextChat = state.chatList[0];
            state.currentChatId = nextChat.id;
            state.currentChat = nextChat;
            state.currentMessages = [];

            // 异步加载新对话的消息
            get().loadChat(nextChat.id);
          } else {
            // 没有其他对话了，清空状态
            state.currentChatId = null;
            state.currentChat = null;
            state.currentMessages = [];
          }
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to delete chat:", error);
      return false;
    }
  },

  // 搜索对话用于引用
  searchChatsForReference: async (query: string) => {
    try {
      const response = await fetch(
        `/api/chats/search?q=${encodeURIComponent(query)}`
      );
      if (!response.ok) {
        throw new Error("Failed to search chats");
      }

      const { data: chats } = await response.json();
      return chats;
    } catch (error) {
      console.error("Failed to search chats:", error);
      return [];
    }
  },
});
