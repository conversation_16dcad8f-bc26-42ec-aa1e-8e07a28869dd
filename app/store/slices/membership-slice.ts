import { StateCreator } from "zustand";
import { AppState } from "../types";

export interface MembershipSlice {
  isMembershipDialogOpen: boolean;
  openMembershipDialog: () => void;
  closeMembershipDialog: () => void;
}

export const createMembershipSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  MembershipSlice
> = (set) => ({
  isMembershipDialogOpen: false,
  openMembershipDialog: () => set({ isMembershipDialogOpen: true }),
  closeMembershipDialog: () => set({ isMembershipDialogOpen: false }),
});
