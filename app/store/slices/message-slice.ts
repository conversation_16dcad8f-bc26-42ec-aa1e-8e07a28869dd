import { StateCreator } from "zustand";
import { AppState, Message } from "../types";

export interface MessageSlice {
  messages: Message[];
  isGenerating: boolean;
  addMessage: (message: Message) => void;
  updateLastMessage: (content: string) => void;
  setIsGenerating: (isGenerating: boolean) => void;
}

export const createMessageSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  MessageSlice
> = (set) => ({
  messages: [],
  isGenerating: false,
  addMessage: (message) =>
    set((state) => {
      state.messages.push(message);
    }),
  updateLastMessage: (content) =>
    set((state) => {
      if (state.messages.length > 0) {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage.role === "assistant") {
          lastMessage.content = content;
        }
      }
    }),
  setIsGenerating: (isGenerating) =>
    set((state) => {
      state.isGenerating = isGenerating;
    }),
});
