import { NextRequest } from "next/server";
import { paymentService } from "@/app/lib/payment";

export async function GET(request: NextRequest) {
  try {
    // 获取所有支付方式信息
    const methods = paymentService.getAllMethodInfo();
    
    return Response.json({
      success: true,
      data: methods,
    });
  } catch (error) {
    console.error("获取支付方式失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to get payment methods",
      },
      { status: 500 }
    );
  }
}
