import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { generateText } from "ai";
import { aiConfig, validateAIConfig } from "@/app/lib/ai-config";

// POST /api/chats/[id]/generate-title - 为对话生成标题
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: chatId } = await params;

    // 验证对话是否属于当前用户
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        userId: session.user.id,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc",
          },
          take: 10, // 只取前10条消息用于生成标题
        },
      },
    });

    if (!chat) {
      return Response.json({ error: "Chat not found" }, { status: 404 });
    }

    // 如果消息太少，不生成标题
    if (chat.messages.length < 2) {
      return Response.json({
        success: true,
        title: "新对话",
      });
    }

    // 验证AI配置
    validateAIConfig();

    // 构建用于生成标题的提示词
    const conversationSummary = chat.messages
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join("\n");

    const titlePrompt = `
请根据以下对话内容，生成一个简洁、准确的对话标题。标题应该：
1. 不超过20个字符
2. 概括对话的主要内容或目的
3. 使用中文
4. 不包含特殊符号

对话内容：
${conversationSummary}

请直接返回标题，不要包含任何其他内容。
`;

    const result = await generateText({
      model: aiConfig.model,
      prompt: titlePrompt,
      temperature: 0.3, // 使用较低的温度以获得更一致的结果
      maxTokens: 50,
    });

    let generatedTitle = result.text.trim();

    // 清理生成的标题
    generatedTitle = generatedTitle
      .replace(/^["']|["']$/g, "") // 移除首尾引号
      .replace(/\n[\s\S]*$/, "") // 只取第一行
      .substring(0, 20); // 限制长度

    // 如果生成的标题为空或太短，使用默认标题
    if (!generatedTitle || generatedTitle.length < 2) {
      generatedTitle = "新对话";
    }

    // 更新对话标题
    await prisma.chat.update({
      where: { id: chatId },
      data: { title: generatedTitle },
    });

    return Response.json({
      success: true,
      title: generatedTitle,
    });
  } catch (error) {
    console.error("Failed to generate chat title:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
