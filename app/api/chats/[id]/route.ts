import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/chats/[id] - 获取特定对话及其消息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    const chat = await prisma.chat.findUnique({
      where: {
        id,
        userId: session.user.id,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!chat) {
      return Response.json(
        { error: "Chat not found" },
        { status: 404 }
      );
    }

    return Response.json({
      success: true,
      data: chat,
    });
  } catch (error) {
    console.error("Failed to load chat:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PATCH /api/chats/[id] - 更新对话信息
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const { title, resumeData } = body;

    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (resumeData !== undefined) updateData.resumeData = resumeData;

    const chat = await prisma.chat.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: updateData,
      select: {
        id: true,
        title: true,
        resumeId: true,
        resumeData: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      success: true,
      data: chat,
    });
  } catch (error) {
    console.error("Failed to update chat:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/chats/[id] - 删除对话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    await prisma.chat.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        isActive: false,
      },
    });

    return Response.json({
      success: true,
    });
  } catch (error) {
    console.error("Failed to delete chat:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
