import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "MEMBERSHIP";
    const active = searchParams.get("active") !== "false"; // 默认只返回激活的商品

    // 查询商品列表
    const products = await prisma.product.findMany({
      where: {
        type: type as any,
        isActive: active,
      },
      orderBy: {
        sortOrder: "asc",
      },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        duration: true,
        type: true,
        sortOrder: true,
      },
    });

    return Response.json({
      success: true,
      data: products,
    });
  } catch (error) {
    console.error("获取商品列表失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to fetch products",
      },
      { status: 500 }
    );
  }
}
