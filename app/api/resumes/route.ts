import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { ResumeSchema, ExtendedResumeData } from "@/app/lib/types";

// GET /api/resumes - 获取用户的简历列表
export async function GET() {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 获取用户的简历列表
    const resumes = await prisma.resume.findMany({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        name: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc", // 按更新时间倒序排列
      },
    });

    return Response.json({
      success: true,
      data: resumes,
    });
  } catch (error) {
    console.error("获取简历列表失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to fetch resumes",
      },
      { status: 500 }
    );
  }
}

// POST /api/resumes - 保存简历
export async function POST(request: NextRequest) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, resumeData, sectionOrder, isDefault = false } = body;

    // 验证必填字段
    if (!name || !resumeData) {
      return Response.json(
        { error: "Name and resume data are required" },
        { status: 400 }
      );
    }

    // 验证简历数据格式
    if (!isValidResumeData(resumeData)) {
      return Response.json(
        { error: "Invalid resume data format" },
        { status: 400 }
      );
    }

    // 构建扩展的简历数据
    const extendedData: ExtendedResumeData = {
      resumeData,
      sectionOrder: sectionOrder || [
        "summary",
        "experience",
        "education",
        "skills",
        "projects",
      ],
    };

    // 如果设置为默认简历，先取消其他默认简历
    if (isDefault) {
      await prisma.resume.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // 创建新简历
    const resume = await prisma.resume.create({
      data: {
        userId: session.user.id,
        name,
        resumeData: extendedData as any,
        isDefault,
      },
      select: {
        id: true,
        name: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      success: true,
      data: resume,
    });
  } catch (error) {
    console.error("保存简历失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to save resume",
      },
      { status: 500 }
    );
  }
}

// 验证简历数据格式
function isValidResumeData(data: any): data is ResumeSchema {
  if (!data || typeof data !== "object") return false;

  // 检查必需的字段
  const requiredFields = [
    "personalInfo",
    "summary",
    "experience",
    "education",
    "skills",
  ];
  for (const field of requiredFields) {
    if (!(field in data)) return false;
  }

  // 检查 personalInfo 结构
  const personalInfo = data.personalInfo;
  if (!personalInfo || typeof personalInfo !== "object") return false;
  if (!personalInfo.name) return false;

  // 检查数组字段
  if (!Array.isArray(data.experience) || !Array.isArray(data.education))
    return false;

  // 检查 skills 结构
  const skills = data.skills;
  if (!skills || typeof skills !== "object") return false;
  // if (!Array.isArray(skills.technical) || !Array.isArray(skills.soft))
  //   return false;

  return true;
}
