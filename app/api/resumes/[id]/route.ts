import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/resumes/[id] - 获取特定简历的完整数据
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // 获取简历数据
    const resume = await prisma.resume.findUnique({
      where: {
        id: id,
      },
      select: {
        id: true,
        userId: true,
        name: true,
        resumeData: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!resume) {
      return Response.json(
        {
          success: false,
          error: "Resume not found",
        },
        { status: 404 }
      );
    }

    // 检查权限 - 只能访问自己的简历
    if (resume.userId !== session.user.id) {
      return Response.json(
        {
          success: false,
          error: "Access denied",
        },
        { status: 403 }
      );
    }

    return Response.json({
      success: true,
      data: resume,
    });
  } catch (error) {
    console.error("获取简历详情失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to fetch resume",
      },
      { status: 500 }
    );
  }
}

// PUT /api/resumes/[id] - 更新简历
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, resumeData, sectionOrder, isDefault } = body;

    // 检查简历是否存在且属于当前用户
    const existingResume = await prisma.resume.findUnique({
      where: { id },
      select: { userId: true },
    });

    if (!existingResume) {
      return Response.json({ error: "Resume not found" }, { status: 404 });
    }

    if (existingResume.userId !== session.user.id) {
      return Response.json({ error: "Access denied" }, { status: 403 });
    }

    // 如果设置为默认简历，先取消其他默认简历
    if (isDefault) {
      await prisma.resume.updateMany({
        where: {
          userId: session.user.id,
          isDefault: true,
          id: { not: id }, // 排除当前简历
        },
        data: {
          isDefault: false,
        },
      });
    }

    // 构建更新数据
    const updateData: any = {};
    if (name) updateData.name = name;
    if (typeof isDefault === "boolean") updateData.isDefault = isDefault;

    // 如果有简历数据更新，构建扩展数据
    if (resumeData) {
      const extendedData = {
        resumeData,
        sectionOrder: sectionOrder || [
          "summary",
          "experience",
          "education",
          "skills",
          "projects",
        ],
      };
      updateData.resumeData = extendedData as any;
    }

    // 更新简历
    const updatedResume = await prisma.resume.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        resumeData: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return Response.json({
      success: true,
      data: updatedResume,
    });
  } catch (error) {
    console.error("更新简历失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to update resume",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/resumes/[id] - 删除简历
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // 检查简历是否存在且属于当前用户
    const existingResume = await prisma.resume.findUnique({
      where: { id },
      select: { userId: true, isDefault: true },
    });

    if (!existingResume) {
      return Response.json({ error: "Resume not found" }, { status: 404 });
    }

    if (existingResume.userId !== session.user.id) {
      return Response.json({ error: "Access denied" }, { status: 403 });
    }

    // 删除简历
    await prisma.resume.delete({
      where: { id },
    });

    return Response.json({
      success: true,
      message: "Resume deleted successfully",
    });
  } catch (error) {
    console.error("删除简历失败:", error);
    return Response.json(
      {
        success: false,
        error: "Failed to delete resume",
      },
      { status: 500 }
    );
  }
}
