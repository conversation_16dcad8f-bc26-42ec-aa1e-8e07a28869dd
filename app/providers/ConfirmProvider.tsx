"use client";

import { createContext, useContext, ReactNode } from "react";
import { useConfirm } from "../hooks/useConfirm";
import { ConfirmDialog } from "../components/common/ConfirmDialog-v2";

interface ConfirmContextType {
  confirm: (options: {
    title: string;
    description: string;
    confirmText: string;
    cancelText?: string;
    variant?: "default" | "destructive";
  }) => Promise<boolean>;
}

const ConfirmContext = createContext<ConfirmContextType | null>(null);

export function ConfirmProvider({ children }: { children: ReactNode }) {
  const { confirm, confirmProps } = useConfirm();

  return (
    <ConfirmContext.Provider value={{ confirm }}>
      {children}
      <ConfirmDialog {...confirmProps} />
    </ConfirmContext.Provider>
  );
}

export function useConfirmDialog() {
  const context = useContext(ConfirmContext);
  if (!context) {
    throw new Error("useConfirmDialog must be used within a ConfirmProvider");
  }
  return context;
}
