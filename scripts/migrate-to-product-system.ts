import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateToProductSystem() {
  console.log('🚀 开始迁移到商品系统...');

  try {
    // 1. 创建默认商品
    console.log('📦 创建默认商品...');
    
    const defaultProduct = await prisma.product.upsert({
      where: { id: 'default-membership-7d' },
      update: {},
      create: {
        id: 'default-membership-7d',
        name: '7天会员',
        description: '享受7天无限制AI简历生成服务',
        price: 5.00,
        duration: 7,
        type: 'MEMBERSHIP',
        isActive: true,
        sortOrder: 1,
      }
    });

    console.log('✅ 默认商品创建成功:', defaultProduct.name);

    // 2. 查询所有现有订单
    const existingOrders = await prisma.order.findMany({
      select: { id: true }
    });

    console.log(`📋 找到 ${existingOrders.length} 个现有订单`);

    if (existingOrders.length > 0) {
      // 3. 为现有订单添加productId字段（临时方案）
      console.log('🔄 更新现有订单...');
      
      // 由于Prisma不支持直接添加必需字段到有数据的表，我们需要使用原生SQL
      await prisma.$executeRaw`
        ALTER TABLE \`Order\` ADD COLUMN \`productId\` VARCHAR(191) DEFAULT '${defaultProduct.id}';
      `;

      await prisma.$executeRaw`
        UPDATE \`Order\` SET \`productId\` = '${defaultProduct.id}' WHERE \`productId\` IS NULL;
      `;

      await prisma.$executeRaw`
        ALTER TABLE \`Order\` MODIFY COLUMN \`productId\` VARCHAR(191) NOT NULL;
      `;

      console.log('✅ 现有订单更新完成');
    }

    // 4. 创建更多商品选项
    console.log('📦 创建更多商品选项...');
    
    const products = [
      {
        id: 'membership-3d',
        name: '3天体验',
        description: '短期体验AI简历生成服务',
        price: 2.99,
        duration: 3,
        type: 'MEMBERSHIP' as const,
        sortOrder: 0,
      },
      {
        id: 'membership-30d',
        name: '30天会员',
        description: '一个月无限制AI简历生成服务',
        price: 15.00,
        duration: 30,
        type: 'MEMBERSHIP' as const,
        sortOrder: 2,
      },
      {
        id: 'membership-90d',
        name: '90天会员',
        description: '三个月无限制AI简历生成服务，更优惠',
        price: 39.00,
        duration: 90,
        type: 'MEMBERSHIP' as const,
        sortOrder: 3,
      }
    ];

    for (const product of products) {
      await prisma.product.upsert({
        where: { id: product.id },
        update: product,
        create: product
      });
      console.log(`✅ 商品创建成功: ${product.name}`);
    }

    console.log('🎉 迁移完成！');
    
    // 5. 显示所有商品
    const allProducts = await prisma.product.findMany({
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log('\n📋 当前商品列表:');
    allProducts.forEach(product => {
      console.log(`- ${product.name}: ¥${product.price} (${product.duration}天)`);
    });

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行迁移
if (require.main === module) {
  migrateToProductSystem()
    .then(() => {
      console.log('✅ 迁移脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

export { migrateToProductSystem };
