import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function setupTestData() {
  console.log("🚀 Setting up test data...");

  try {
    // 清理现有测试数据
    console.log("🧹 Cleaning existing test data...");
    await prisma.payment.deleteMany({
      where: {
        order: {
          user: {
            phone: {
              startsWith: "test_",
            },
          },
        },
      },
    });

    await prisma.order.deleteMany({
      where: {
        user: {
          phone: {
            startsWith: "test_",
          },
        },
      },
    });

    await prisma.user.deleteMany({
      where: {
        phone: {
          startsWith: "test_",
        },
      },
    });

    // 创建测试用户
    console.log("👤 Creating test users...");

    // 免费用户 - 0次聊天
    const freeUser1 = await prisma.user.create({
      data: {
        phone: "test_free_user_1",
        name: "测试免费用户1",
        chatCount: 0,
        isMember: false,
        phoneVerified: new Date(),
      },
    });

    // 免费用户 - 3次聊天
    const freeUser2 = await prisma.user.create({
      data: {
        phone: "test_free_user_2",
        name: "测试免费用户2",
        chatCount: 3,
        isMember: false,
        phoneVerified: new Date(),
      },
    });

    // 免费用户 - 5次聊天（已达限制）
    const freeUser3 = await prisma.user.create({
      data: {
        phone: "test_free_user_3",
        name: "测试免费用户3",
        chatCount: 5,
        isMember: false,
        phoneVerified: new Date(),
      },
    });

    // 会员用户 - 有效会员
    const memberUser1 = await prisma.user.create({
      data: {
        phone: "test_member_user_1",
        name: "测试会员用户1",
        chatCount: 10,
        isMember: true,
        memberExpiry: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
        phoneVerified: new Date(),
      },
    });

    // 会员用户 - 过期会员
    const expiredMemberUser = await prisma.user.create({
      data: {
        phone: "test_expired_member",
        name: "测试过期会员",
        chatCount: 15,
        isMember: true,
        memberExpiry: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前过期
        phoneVerified: new Date(),
      },
    });

    // 创建测试商品
    console.log("🛍️ Creating test products...");

    const membershipProduct = await prisma.product.create({
      data: {
        name: "7天会员",
        description: "7天会员权益，无限制聊天",
        price: 5.0,
        duration: 7,
        type: "MEMBERSHIP",
        isActive: true,
        sortOrder: 1,
      },
    });

    // 创建测试订单
    console.log("📦 Creating test orders...");

    // 已支付订单
    const paidOrder = await prisma.order.create({
      data: {
        userId: memberUser1.id,
        productId: membershipProduct.id,
        amount: 5.0,
        status: "PAID",
        duration: 7,
        outTradeNo: "TEST_ORDER_PAID_001",
        tradeNo: "TEST_TRADE_001",
        paidAt: new Date(),
      },
    });

    await prisma.payment.create({
      data: {
        orderId: paidOrder.id,
        amount: 5.0,
        method: "ALIPAY",
        status: "SUCCESS",
        tradeNo: "TEST_TRADE_001",
      },
    });

    // 待支付订单
    const pendingOrder = await prisma.order.create({
      data: {
        userId: freeUser3.id,
        productId: membershipProduct.id,
        amount: 5.0,
        status: "PENDING",
        duration: 7,
        outTradeNo: "TEST_ORDER_PENDING_001",
      },
    });

    await prisma.payment.create({
      data: {
        orderId: pendingOrder.id,
        amount: 5.0,
        method: "ALIPAY",
        status: "PENDING",
      },
    });

    // 已取消订单
    const cancelledOrder = await prisma.order.create({
      data: {
        userId: freeUser2.id,
        productId: membershipProduct.id,
        amount: 5.0,
        status: "CANCELLED",
        duration: 7,
        outTradeNo: "TEST_ORDER_CANCELLED_001",
      },
    });

    await prisma.payment.create({
      data: {
        orderId: cancelledOrder.id,
        amount: 5.0,
        method: "ALIPAY",
        status: "CANCELLED",
      },
    });

    console.log("✅ Test data setup completed!");
    console.log("\n📊 Created test data:");
    console.log(`- ${5} test users`);
    console.log(`- ${3} test orders`);
    console.log(`- ${3} test payments`);

    console.log("\n👥 Test Users:");
    console.log("1. test_free_user_1 - 免费用户，0次聊天");
    console.log("2. test_free_user_2 - 免费用户，3次聊天");
    console.log("3. test_free_user_3 - 免费用户，5次聊天（已达限制）");
    console.log("4. test_member_user_1 - 有效会员用户");
    console.log("5. test_expired_member - 过期会员用户");

    console.log("\n🧪 Testing URLs:");
    console.log("- Main App: http://localhost:3000");
    console.log("- Test Page: http://localhost:3000/test-membership");
    console.log("- API Status: http://localhost:3000/api/membership/status");
  } catch (error) {
    console.error("❌ Error setting up test data:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function resetUserChatCount(phone: string) {
  try {
    const user = await prisma.user.update({
      where: { phone },
      data: { chatCount: 0 },
    });
    console.log(`✅ Reset chat count for user: ${user.name || user.phone}`);
  } catch (error) {
    console.error(`❌ Failed to reset chat count for ${phone}:`, error);
  }
}

async function activateTestMembership(phone: string, days: number = 7) {
  try {
    const user = await prisma.user.update({
      where: { phone },
      data: {
        isMember: true,
        memberExpiry: new Date(Date.now() + days * 24 * 60 * 60 * 1000),
      },
    });
    console.log(
      `✅ Activated ${days}-day membership for user: ${user.name || user.phone}`
    );
  } catch (error) {
    console.error(`❌ Failed to activate membership for ${phone}:`, error);
  }
}

// 命令行参数处理
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];

switch (command) {
  case "setup":
    setupTestData();
    break;
  case "reset-chat":
    if (!arg1) {
      console.error(
        "❌ Please provide phone number: npm run test-setup reset-chat <phone>"
      );
      process.exit(1);
    }
    resetUserChatCount(arg1).then(() => prisma.$disconnect());
    break;
  case "activate-member":
    if (!arg1) {
      console.error(
        "❌ Please provide phone number: npm run test-setup activate-member <phone> [days]"
      );
      process.exit(1);
    }
    const days = arg2 ? parseInt(arg2) : 7;
    activateTestMembership(arg1, days).then(() => prisma.$disconnect());
    break;
  default:
    console.log("🔧 Available commands:");
    console.log("- setup: Create test data");
    console.log("- reset-chat <phone>: Reset user chat count to 0");
    console.log(
      "- activate-member <phone> [days]: Activate membership for user"
    );
    console.log("\nExample:");
    console.log("npm run test-setup setup");
    console.log("npm run test-setup reset-chat test_free_user_1");
    console.log("npm run test-setup activate-member test_free_user_1 7");
}
