# 火山引擎 AI 配置指南

## 概述

本项目已成功配置为使用火山引擎的 OpenAI 兼容接口，通过 Vercel AI SDK 调用 DeepSeek-V3 模型。

## 配置详情

### 环境变量配置

在 `.env.local` 文件中配置以下变量：

```bash
# 火山引擎 API 配置
OPENAI_API_KEY=74953cf1-2fa0-4753-b1e5-c5c618a0237a
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
OPENAI_MODEL=deepseek-v3-250324

# 其他配置...
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here
DATABASE_URL="mysql://root:zxc123456@localhost:3306/resume_next"
```

### 技术实现

#### 1. AI 配置 (`app/lib/ai-config.ts`)

```typescript
import { createOpenAI } from "@ai-sdk/openai";

// Create Volcano Engine OpenAI-compatible client
const volcanoOpenAI = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
  baseURL: process.env.OPENAI_BASE_URL || "https://ark.cn-beijing.volces.com/api/v3",
});

// AI Configuration for Volcano Engine (OpenAI-compatible)
export const aiConfig = {
  model: volcanoOpenAI(process.env.OPENAI_MODEL || "deepseek-v3-250324"),
  temperature: 0.7,
  maxTokens: 2000,
};
```

#### 2. 关键特性

- **OpenAI 兼容接口**：使用 `createOpenAI` 创建自定义客户端
- **环境变量驱动**：支持灵活的配置管理
- **流式响应**：支持实时 AI 响应流
- **错误处理**：完善的错误处理和回退机制

## 测试验证

### 1. 运行测试脚本

```bash
node test-volcano-ai.js
```

预期输出：
```
🔥 Testing Volcano Engine AI Configuration...

📋 Environment Variables:
OPENAI_API_KEY: ✅ Set
OPENAI_BASE_URL: https://ark.cn-beijing.volces.com/api/v3
OPENAI_MODEL: deepseek-v3-250324

🚀 Testing AI model with a simple prompt...

📝 AI Response:
我是DeepSeek Chat，由深度求索公司开发的AI助手，擅长中文交流，能回答问题、提供建议，免费且支持长文本处理。

✅ Test completed successfully!
📊 Response length: 60 characters
```

### 2. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 测试完整功能。

## API 端点

### 1. 普通聊天 API
- **端点**：`POST /api/chat`
- **功能**：一次性返回完整响应

### 2. 流式聊天 API
- **端点**：`POST /api/chat/stream`
- **功能**：实时流式返回响应

### 请求格式
```json
{
  "userInput": "请帮我生成一份软件工程师的简历",
  "resumeData": null,
  "locale": "zh"
}
```

## 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查火山引擎控制台中的 API 密钥
   - 确保密钥有足够的使用额度

2. **模型不可用**
   - 验证 `OPENAI_MODEL` 配置是否正确
   - 确认模型在火山引擎中可用

3. **网络连接问题**
   - 检查 `OPENAI_BASE_URL` 是否正确
   - 确认网络可以访问火山引擎服务

### 调试技巧

1. **查看控制台日志**：开发环境下所有 API 调用都有详细日志
2. **使用测试脚本**：运行 `test-volcano-ai.js` 快速验证配置
3. **检查环境变量**：确保所有必需的环境变量都已正确设置

## 优势

1. **成本效益**：火山引擎提供有竞争力的定价
2. **高性能**：DeepSeek-V3 模型性能优秀
3. **兼容性**：完全兼容 OpenAI API 格式
4. **稳定性**：火山引擎提供稳定的服务

## 下一步

- 可以根据需要调整模型参数（temperature、maxTokens）
- 可以尝试其他火山引擎支持的模型
- 可以添加更多的错误处理和重试机制

---

**注意**：请妥善保管您的 API 密钥，不要将其提交到版本控制系统中。
