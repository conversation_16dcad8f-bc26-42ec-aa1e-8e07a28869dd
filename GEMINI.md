# GEMINI 项目指南

本文档提供了 Gemini AI 助手在此项目上工作时应遵循的指南。

## 项目概览

这是一个 Next.js 应用程序，作为 AI 驱动的简历生成器。用户可以与 AI 助手交互，实时生成和编辑他们的简历。该应用程序包括用户认证、会员计划和在线支付等功能。它支持英语和中文两种语言。

## 技术栈

- **框架**：Next.js (App Router)
- **语言**：TypeScript
- **样式**：Tailwind CSS 与 shadcn/ui 组件
- **状态管理**：Zustand
- **国际化 (i18n)**：`next-intl`
- **认证**：NextAuth.js
- **ORM**：Prisma
- **代码检查**：Next.js 默认代码检查器

## 关键文件和目录

- `app/[locale]/page.tsx`：主页面组件，布局聊天和简历预览区域。
- `app/api/auth/[...nextauth]/route.ts`：NextAuth.js 路由，用于处理用户认证。
- `app/api/chat/route.ts`：处理聊天消息和简历数据更新的 API 路由。AI 逻辑现已抽象到 `app/lib/ai-service.ts`。
- `app/api/chat/stream/route.ts`：用于聊天消息的流式 API 路由。AI 逻辑现已抽象到 `app/lib/ai-service.ts`。
- `app/api/membership/`：与用户会员状态相关的 API 路由。
- `app/api/payment/`：用于处理 Stripe 和 Alipay 等支付提供商的支付的 API 路由。
- `app/components/chat-area.tsx`：AI 聊天界面的主组件，现已拆分为 `app/components/chat/` 下更小、更集中的子组件。
- `app/components/resume-preview.tsx`：显示简历的主组件，现已拆分为 `app/components/resume/sections/` 下更小、更集中的子组件。
- `app/store/`：Zustand 状态管理目录，现已模块化为切片 (slices)：
  - `app/store/index.ts`：主存储入口点，组合所有切片。
  - `app/store/slices/`：包含独立的存储切片（例如，`auth-slice.ts`、`message-slice.ts`、`resume-data-slice.ts`、`resume-layout-slice.ts`、`resume-management-slice.ts`）。
  - `app/store/defaults.ts`：包含默认简历数据。
  - `app/store/types.ts`：定义共享存储类型。
- `app/lib/prompts.ts`：包含所有 AI 提示定义。
- `app/lib/ai-service.ts`：封装 AI 模型交互逻辑（提示创建、API 调用、响应解析）。
- `app/hooks/useAuthForm.ts`：用于处理认证表单逻辑的自定义 Hook。
- `app/components/membership/ProductSelector.tsx`：用于选择会员产品的组件。
- `prisma/schema.prisma`：定义数据库模型的 Prisma 模式文件。
- `lib/auth.ts`：包含认证相关的配置和实用程序。
- `lib/prisma.ts`：Prisma 客户端实例。
- `middleware.ts`：处理请求中间件，包括国际化和路由保护。
- `messages/{en,zh}.json`：`next-intl` 的语言文件。
- `i18n/`：`next-intl` 的配置。

## 开发工作流程

1.  **运行开发服务器**：
    ```bash
    yarn dev
    ```
2.  **代码检查**：
    ```bash
    yarn lint
    ```
3.  **构建生产版本**：
    ```bash
    yarn build
    ```
4.  **运行数据库迁移**：
    ```bash
    npx prisma migrate dev
    ```

## 如何贡献

- 遵循现有的代码风格和约定。
- 对于 UI 更改，尽可能使用现有的 `shadcn/ui` 组件。
- 对于状态管理，使用 `useAppStore` Zustand 存储,Zustand 请记得使用 zustand/immer。
- 对于数据库模式更改，修改 `prisma/schema.prisma` 并创建新的迁移。
- 对于国际化，将新键添加到 `messages/en.json` 和 `messages/zh.json` 文件中。
- 对于 API 更改，修改 `app/api/` 下的相关文件。
- 对于用户提出的需求请先制定详细的计划，并说明为什么这样做，然后逐步执行工作。
- 请确保所有的代码都是官方推荐的最佳实践，对于最佳实践不了解的可以使用 context7 进行了解
