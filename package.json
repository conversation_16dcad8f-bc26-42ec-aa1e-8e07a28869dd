{"name": "resume-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test-setup": "tsx scripts/test-setup.ts", "db:push": "prisma db push", "db:generate": "prisma generate"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@auth/prisma-adapter": "^2.10.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^2.4.6", "ai": "^4.3.16", "alipay-sdk": "^4.14.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "geist": "^1.4.2", "immer": "^10.1.1", "lucide-react": "^0.523.0", "mysql2": "^3.14.1", "next": "15.3.2", "next-auth": "^4.24.11", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}